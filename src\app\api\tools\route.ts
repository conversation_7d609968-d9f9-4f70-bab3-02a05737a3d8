import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db/mongodb';
import { Tool } from '@/lib/db/models';
import { TOOLS } from '@/lib/constants/tools';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const source = searchParams.get('source') || 'auto'; // auto, database, constants
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const limit = parseInt(searchParams.get('limit') || '50');
    const page = parseInt(searchParams.get('page') || '1');

    if (source === 'constants') {
      // 直接从常量文件返回数据
      let tools = [...TOOLS];

      // 分类筛选
      if (category) {
        tools = tools.filter(tool => tool.category === category);
      }

      // 搜索筛选
      if (search) {
        const searchLower = search.toLowerCase();
        tools = tools.filter(tool => 
          tool.name.toLowerCase().includes(searchLower) ||
          tool.description.toLowerCase().includes(searchLower) ||
          tool.tags.some(tag => tag.toLowerCase().includes(searchLower))
        );
      }

      // 分页
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedTools = tools.slice(startIndex, endIndex);

      return NextResponse.json({
        success: true,
        data: {
          tools: paginatedTools,
          total: tools.length,
          page,
          limit,
          totalPages: Math.ceil(tools.length / limit),
          source: 'constants',
        },
      });
    }

    // 尝试从数据库获取数据
    try {
      await connectDB();

      // 构建查询条件
      const query: any = { isPublic: true };

      if (category) {
        query.category = category;
      }

      if (search) {
        query.$or = [
          { name: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } },
          { tags: { $in: [new RegExp(search, 'i')] } },
        ];
      }

      // 获取总数
      const total = await Tool.countDocuments(query);

      // 获取工具数据
      const tools = await Tool.find(query)
        .select('-__v')
        .sort({ category: 1, name: 1 })
        .limit(limit)
        .skip((page - 1) * limit)
        .lean();

      return NextResponse.json({
        success: true,
        data: {
          tools,
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
          source: 'database',
        },
      });

    } catch (dbError) {
      console.error('数据库查询失败，回退到常量数据:', dbError);

      if (source === 'database') {
        // 如果明确要求从数据库获取但失败了，返回错误
        return NextResponse.json({
          success: false,
          message: '数据库连接失败',
          error: dbError instanceof Error ? dbError.message : '未知错误',
        }, { status: 500 });
      }

      // 自动回退到常量数据
      let tools = [...TOOLS];

      // 分类筛选
      if (category) {
        tools = tools.filter(tool => tool.category === category);
      }

      // 搜索筛选
      if (search) {
        const searchLower = search.toLowerCase();
        tools = tools.filter(tool => 
          tool.name.toLowerCase().includes(searchLower) ||
          tool.description.toLowerCase().includes(searchLower) ||
          tool.tags.some(tag => tag.toLowerCase().includes(searchLower))
        );
      }

      // 分页
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedTools = tools.slice(startIndex, endIndex);

      return NextResponse.json({
        success: true,
        data: {
          tools: paginatedTools,
          total: tools.length,
          page,
          limit,
          totalPages: Math.ceil(tools.length / limit),
          source: 'constants_fallback',
        },
        warning: '数据库连接失败，使用常量数据',
      });
    }

  } catch (error) {
    console.error('获取工具列表失败:', error);

    return NextResponse.json({
      success: false,
      message: '获取工具列表失败',
      error: error instanceof Error ? error.message : '未知错误',
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const { action, toolId } = body;

    if (action === 'visit') {
      // 增加工具访问次数
      if (!toolId) {
        return NextResponse.json({
          success: false,
          message: '缺少工具ID',
        }, { status: 400 });
      }

      const tool = await Tool.findOneAndUpdate(
        { $or: [{ _id: toolId }, { path: toolId }] },
        { 
          $inc: { visitCount: 1 },
          lastVisited: new Date(),
        },
        { new: true }
      );

      if (!tool) {
        return NextResponse.json({
          success: false,
          message: '工具不存在',
        }, { status: 404 });
      }

      return NextResponse.json({
        success: true,
        message: '访问次数已更新',
        data: {
          toolId: tool._id,
          visitCount: tool.visitCount,
        },
      });
    }

    return NextResponse.json({
      success: false,
      message: '不支持的操作',
      availableActions: ['visit'],
    }, { status: 400 });

  } catch (error) {
    console.error('工具操作失败:', error);

    return NextResponse.json({
      success: false,
      message: '工具操作失败',
      error: error instanceof Error ? error.message : '未知错误',
    }, { status: 500 });
  }
}
