'use client';

import React, { useState } from 'react';
import { useSession } from 'next-auth/react';
import { <PERSON><PERSON>, Card, CardContent, CardHeader, CardTitle } from '@/components/ui';

type FeedbackType = 'bug' | 'feature' | 'improvement' | 'other';
type Priority = 'low' | 'medium' | 'high';

interface FeedbackForm {
  type: FeedbackType;
  title: string;
  description: string;
  priority: Priority;
  email: string;
  attachments?: File[];
}

const FeedbackPage: React.FC = () => {
  const { data: session } = useSession();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const [form, setForm] = useState<FeedbackForm>({
    type: 'improvement',
    title: '',
    description: '',
    priority: 'medium',
    email: session?.user?.email || '',
    attachments: [],
  });

  const feedbackTypes = [
    { value: 'bug' as FeedbackType, label: '🐛 Bug报告', description: '发现了系统错误或异常' },
    { value: 'feature' as FeedbackType, label: '✨ 功能建议', description: '希望添加新功能' },
    { value: 'improvement' as FeedbackType, label: '🚀 改进建议', description: '对现有功能的改进意见' },
    { value: 'other' as FeedbackType, label: '💬 其他反馈', description: '其他意见或建议' },
  ];

  const priorities = [
    { value: 'low' as Priority, label: '低优先级', color: 'text-green-600 bg-green-50' },
    { value: 'medium' as Priority, label: '中优先级', color: 'text-yellow-600 bg-yellow-50' },
    { value: 'high' as Priority, label: '高优先级', color: 'text-red-600 bg-red-50' },
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // 这里应该调用API提交反馈
      // const response = await fetch('/api/feedback', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(form),
      // });

      // 模拟提交
      await new Promise(resolve => setTimeout(resolve, 2000));

      setSubmitted(true);
    } catch (error) {
      console.error('提交反馈失败:', error);
      alert('提交失败，请稍后重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setForm(prev => ({ ...prev, attachments: files }));
  };

  if (submitted) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto text-center">
          <div className="text-6xl mb-6">🎉</div>
          <h1 className="text-3xl font-bold text-gray-900 mb-4">反馈提交成功！</h1>
          <p className="text-gray-600 mb-8">
            感谢您的宝贵意见！我们会认真考虑您的建议，并尽快回复。
          </p>
          <div className="space-x-4">
            <Button onClick={() => setSubmitted(false)}>
              提交更多反馈
            </Button>
            <Button variant="outline" onClick={() => window.location.href = '/'}>
              返回首页
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">意见反馈</h1>
          <p className="text-gray-600">
            您的反馈对我们非常重要，帮助我们不断改进产品体验
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 反馈表单 */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>提交反馈</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* 反馈类型 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      反馈类型
                    </label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {feedbackTypes.map((type) => (
                        <div
                          key={type.value}
                          className={`p-4 border rounded-lg cursor-pointer transition-all ${
                            form.type === type.value
                              ? 'border-primary-500 bg-primary-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => setForm(prev => ({ ...prev, type: type.value }))}
                        >
                          <div className="font-medium text-gray-900">{type.label}</div>
                          <div className="text-sm text-gray-500 mt-1">{type.description}</div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 标题 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      标题 *
                    </label>
                    <input
                      type="text"
                      required
                      value={form.title}
                      onChange={(e) => setForm(prev => ({ ...prev, title: e.target.value }))}
                      placeholder="请简要描述您的反馈..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>

                  {/* 详细描述 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      详细描述 *
                    </label>
                    <textarea
                      required
                      rows={6}
                      value={form.description}
                      onChange={(e) => setForm(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="请详细描述您遇到的问题或建议..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>

                  {/* 优先级 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      优先级
                    </label>
                    <div className="flex space-x-3">
                      {priorities.map((priority) => (
                        <button
                          key={priority.value}
                          type="button"
                          onClick={() => setForm(prev => ({ ...prev, priority: priority.value }))}
                          className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                            form.priority === priority.value
                              ? priority.color
                              : 'text-gray-600 bg-gray-100 hover:bg-gray-200'
                          }`}
                        >
                          {priority.label}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* 联系邮箱 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      联系邮箱 *
                    </label>
                    <input
                      type="email"
                      required
                      value={form.email}
                      onChange={(e) => setForm(prev => ({ ...prev, email: e.target.value }))}
                      placeholder="用于接收回复的邮箱地址"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>

                  {/* 附件上传 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      附件 (可选)
                    </label>
                    <input
                      type="file"
                      multiple
                      accept="image/*,.pdf,.doc,.docx,.txt"
                      onChange={handleFileChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      支持图片、PDF、Word文档等格式，最多5个文件
                    </p>
                  </div>

                  {/* 提交按钮 */}
                  <div className="flex justify-end">
                    <Button
                      type="submit"
                      disabled={isSubmitting || !form.title || !form.description || !form.email}
                      className="min-w-32"
                    >
                      {isSubmitting ? '提交中...' : '提交反馈'}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* 侧边栏信息 */}
          <div className="space-y-6">
            {/* 反馈指南 */}
            <Card>
              <CardHeader>
                <CardTitle>反馈指南</CardTitle>
              </CardHeader>
              <CardContent className="text-sm text-gray-600 space-y-3">
                <div>
                  <h4 className="font-medium text-gray-900">🐛 Bug报告</h4>
                  <p>请详细描述问题的重现步骤、预期结果和实际结果</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">✨ 功能建议</h4>
                  <p>描述您希望的功能、使用场景和预期效果</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">🚀 改进建议</h4>
                  <p>说明现有功能的不足之处和改进方向</p>
                </div>
              </CardContent>
            </Card>

            {/* 联系方式 */}
            <Card>
              <CardHeader>
                <CardTitle>其他联系方式</CardTitle>
              </CardHeader>
              <CardContent className="text-sm text-gray-600 space-y-2">
                <div className="flex items-center space-x-2">
                  <span>📧</span>
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center space-x-2">
                  <span>💬</span>
                  <span>在线客服 (工作日 9:00-18:00)</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span>📞</span>
                  <span>技术支持热线</span>
                </div>
              </CardContent>
            </Card>

            {/* 响应时间 */}
            <Card>
              <CardHeader>
                <CardTitle>响应时间</CardTitle>
              </CardHeader>
              <CardContent className="text-sm text-gray-600 space-y-2">
                <div className="flex justify-between">
                  <span>🔴 高优先级</span>
                  <span>24小时内</span>
                </div>
                <div className="flex justify-between">
                  <span>🟡 中优先级</span>
                  <span>3个工作日内</span>
                </div>
                <div className="flex justify-between">
                  <span>🟢 低优先级</span>
                  <span>1周内</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeedbackPage;
