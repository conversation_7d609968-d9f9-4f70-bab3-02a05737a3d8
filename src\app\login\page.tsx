'use client';

import React, { useState } from 'react';
import { signIn } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Button, Card, CardContent, CardHeader, CardTitle, Input } from '@/components/ui';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
      });

      if (result?.error) {
        setError('登录失败，请检查邮箱和密码');
      } else {
        // 登录成功，重定向到首页
        router.push('/');
        router.refresh();
      }
    } catch {
      setError('登录过程中发生错误');
    } finally {
      setLoading(false);
    }
  };

  const handleDemoLogin = async (userType: 'admin' | 'user') => {
    setLoading(true);
    setError('');

    // 从环境变量获取演示账户信息
    const credentials = {
      admin: {
        email: process.env.NEXT_PUBLIC_DEMO_ADMIN_EMAIL || '<EMAIL>',
        password: process.env.NEXT_PUBLIC_DEMO_ADMIN_PASSWORD || 'admin123'
      },
      user: {
        email: process.env.NEXT_PUBLIC_DEMO_USER_EMAIL || '<EMAIL>',
        password: process.env.NEXT_PUBLIC_DEMO_USER_PASSWORD || 'user123'
      },
    };

    try {
      const result = await signIn('credentials', {
        email: credentials[userType].email,
        password: credentials[userType].password,
        redirect: false,
      });

      if (result?.error) {
        setError('演示登录失败');
      } else {
        router.push('/');
        router.refresh();
      }
    } catch {
      setError('登录过程中发生错误');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            登录到 Tool List
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            使用您的账户登录，或尝试演示账户
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>账户登录</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                  {error}
                </div>
              )}

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                  邮箱地址
                </label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  placeholder="请输入邮箱地址"
                  className="mt-1"
                />
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  密码
                </label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  placeholder="请输入密码"
                  className="mt-1"
                />
              </div>

              <Button
                type="submit"
                disabled={loading}
                className="w-full"
              >
                {loading ? '登录中...' : '登录'}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* 演示账户 - 仅在开发环境显示 */}
        {process.env.NODE_ENV === 'development' && (
          <Card>
            <CardHeader>
              <CardTitle>演示账户</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="text-sm text-gray-600 mb-4">
                  您可以使用演示账户快速体验，或者注册新账户：
                </div>

                <div className="grid grid-cols-1 gap-3">
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="font-medium text-blue-900">管理员账户</div>
                        <div className="text-sm text-blue-700">演示管理员</div>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDemoLogin('admin')}
                        disabled={loading}
                      >
                        登录
                      </Button>
                    </div>
                  </div>

                  <div className="p-3 bg-green-50 rounded-lg">
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="font-medium text-green-900">普通用户</div>
                        <div className="text-sm text-green-700">演示用户</div>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDemoLogin('user')}
                        disabled={loading}
                      >
                        登录
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 注册链接 */}
        <div className="text-center space-y-2">
          <p className="text-sm text-gray-600">
            还没有账户？{' '}
            <button
              onClick={() => router.push('/register')}
              className="font-medium text-blue-600 hover:text-blue-500"
            >
              立即注册
            </button>
          </p>
          <Button
            variant="ghost"
            onClick={() => router.push('/')}
            className="text-sm"
          >
            ← 返回首页
          </Button>
        </div>
      </div>
    </div>
  );
}
