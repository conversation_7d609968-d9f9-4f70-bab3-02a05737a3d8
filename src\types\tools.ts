// 工具相关类型定义

export interface Tool {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: string;
  tags: string[];
  path: string;
  isPublic: boolean;
  requiredAuth: boolean;
  visitCount: number;
  lastVisited?: Date;
  addedBy: string;
  config?: ToolConfig;
  createdAt: Date;
  updatedAt: Date;
}

export interface ToolConfig {
  maxInputSize?: number;
  allowedFileTypes?: string[];
  rateLimit?: {
    requests: number;
    window: number;
  };
}

export interface ToolCategory {
  id: string;
  name: string;
  icon: string;
  color: string;
  description: string;
  parentId?: string;
  order: number;
  isPublic: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ToolModule {
  id: string;
  name: string;
  description: string;
  category: string;
  isPublic: boolean;
  requiredAuth: boolean;
  component: React.ComponentType<ToolComponentProps>;
  api?: {
    endpoint: string;
    methods: string[];
  };
}

export interface ToolComponentProps {
  toolId: string;
  config?: ToolConfig;
  onUsage?: (action: string, metadata?: Record<string, unknown>) => void;
}

// 时间戳转换工具
export interface TimestampConverterState {
  input: string;
  output: string;
  inputType: 'timestamp' | 'datetime';
  outputFormat: 'iso' | 'local' | 'utc' | 'custom';
  customFormat?: string;
  timezone?: string;
}

// JSON 格式化工具
export interface JsonFormatterState {
  input: string;
  output: string;
  indent: number;
  sortKeys: boolean;
  validateOnly: boolean;
  error?: string;
}

// 文本转换工具
export interface TextConverterState {
  input: string;
  output: string;
  operation: 'uppercase' | 'lowercase' | 'capitalize' | 'camelCase' | 'snakeCase' | 'kebabCase' | 'base64Encode' | 'base64Decode' | 'urlEncode' | 'urlDecode' | 'htmlEncode' | 'htmlDecode';
}

// IP 地址转换工具
export interface IpConverterState {
  input: string;
  output: string;
  inputType: 'ipv4' | 'decimal' | 'hex' | 'binary';
  outputType: 'ipv4' | 'decimal' | 'hex' | 'binary';
  error?: string;
}

// 工具使用统计
export interface ToolUsage {
  toolId: string;
  userId?: string;
  sessionId: string;
  action: 'view' | 'use' | 'download' | 'share' | 'favorite';
  metadata?: {
    userAgent?: string;
    ip?: string;
    referrer?: string;
    duration?: number;
    inputSize?: number;
    outputSize?: number;
  };
  createdAt: Date;
}

// 工具搜索参数
export interface ToolSearchParams {
  query?: string;
  category?: string;
  tags?: string[];
  isPublic?: boolean;
  requiredAuth?: boolean;
  sortBy?: 'name' | 'visitCount' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

// 工具搜索结果
export interface ToolSearchResult {
  tools: Tool[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 工具操作结果
export interface ToolOperationResult<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  metadata?: Record<string, unknown>;
}

// 工具输入输出面板属性
export interface IOPanelProps {
  title: string;
  placeholder?: string;
  value: string;
  onChange?: (value: string) => void;
  readonly?: boolean;
  language?: string;
  actions?: Array<{
    label: string;
    icon: string;
    onClick: () => void;
    disabled?: boolean;
  }>;
  maxLength?: number;
  showLineNumbers?: boolean;
  error?: string;
  className?: string;
}

// 工具卡片属性
export interface ToolCardProps {
  tool: Tool;
  size?: 'small' | 'medium' | 'large';
  onClick: (toolId: string) => void;
  onFavorite?: (toolId: string) => void;
  isFavorite?: boolean;
  className?: string;
}
