import React from 'react';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui';

export default function AboutPage() {
  return (
    <div className="py-6">
      {/* 页面头部 */}
      <div className="mb-6">
        {/* 面包屑导航 */}
        <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-4">
          <Link href="/" className="hover:text-gray-700">首页</Link>
          <span>/</span>
          <span className="text-gray-900">关于我们</span>
        </nav>

        <h1 className="text-3xl font-bold text-gray-900">关于 Tool List</h1>
      </div>

      {/* 关于内容 */}
      <div className="space-y-6">
        <Card>
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-4">项目简介</h2>
            <p className="text-gray-600 leading-relaxed">
              Tool List 是一个免费的在线工具集合平台，致力于为开发者和普通用户提供实用、高效的在线工具。
              我们的目标是让复杂的任务变得简单，让日常工作更加高效。
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-4">功能特色</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium text-gray-900 mb-2">🕒 时间工具</h3>
                <p className="text-gray-600 text-sm">Unix时间戳转换、时区转换等</p>
              </div>
              <div>
                <h3 className="font-medium text-gray-900 mb-2">📝 文本工具</h3>
                <p className="text-gray-600 text-sm">大小写转换、编码解码等</p>
              </div>
              <div>
                <h3 className="font-medium text-gray-900 mb-2">🔧 格式化工具</h3>
                <p className="text-gray-600 text-sm">JSON、XML、CSS格式化</p>
              </div>
              <div>
                <h3 className="font-medium text-gray-900 mb-2">🔐 加密工具</h3>
                <p className="text-gray-600 text-sm">MD5、SHA、Base64等</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-4">技术栈</h2>
            <div className="flex flex-wrap gap-2">
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">Next.js 15</span>
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">TypeScript</span>
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">Tailwind CSS</span>
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">MongoDB</span>
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">NextAuth.js</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-4">联系我们</h2>
            <p className="text-gray-600 leading-relaxed mb-4">
              如果您有任何问题、建议或需要技术支持，欢迎通过以下方式联系我们。
            </p>
            <div className="flex space-x-4">
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
              >
                📧 邮箱联系
              </a>
              <Link
                href="/feedback"
                className="inline-flex items-center px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
              >
                💬 意见反馈
              </Link>
              <Link
                href="/tools"
                className="inline-flex items-center px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
              >
                🔧 浏览工具
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 返回首页 */}
      <div className="mt-8 text-center">
        <Link
          href="/"
          className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
        >
          ← 返回首页
        </Link>
      </div>
    </div>
  );
}
