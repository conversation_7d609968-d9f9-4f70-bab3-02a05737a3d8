'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Card, CardContent, Button } from '@/components/ui';
import ShareButton from '@/components/share/ShareButton';

export default function CaseConverterPage() {
  const [textInput, setTextInput] = useState('');
  const [result, setResult] = useState('');
  const [currentCaseType, setCurrentCaseType] = useState('');
  const [copySuccess, setCopySuccess] = useState<{
    show: boolean;
    message: string;
    id: number;
  }>({
    show: false,
    message: '',
    id: 0
  });

  const convertCase = (type: string) => {
    if (!textInput.trim()) {
      setResult('');
      setCurrentCaseType('');
      return;
    }

    let convertedText = '';

    switch (type) {
      case 'upper':
        convertedText = textInput.toUpperCase();
        break;
      case 'lower':
        convertedText = textInput.toLowerCase();
        break;
      case 'title':
        convertedText = textInput.toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
        break;
      case 'camel':
        convertedText = textInput.toLowerCase()
          .replace(/[^a-zA-Z0-9]+(.)/g, (m, chr) => chr.toUpperCase());
        break;
      case 'pascal':
        convertedText = textInput.toLowerCase()
          .replace(/[^a-zA-Z0-9]+(.)/g, (m, chr) => chr.toUpperCase())
          .replace(/^./, str => str.toUpperCase());
        break;
      case 'snake':
        convertedText = textInput.toLowerCase()
          .replace(/[^a-zA-Z0-9]+/g, '_')
          .replace(/^_+|_+$/g, '');
        break;
      case 'kebab':
        convertedText = textInput.toLowerCase()
          .replace(/[^a-zA-Z0-9]+/g, '-')
          .replace(/^-+|-+$/g, '');
        break;
      default:
        convertedText = textInput;
    }

    setResult(convertedText);
    setCurrentCaseType(type);
  };

  // 显示复制成功提示
  const showCopySuccess = (text: string) => {
    const id = Date.now();

    // 截断长文本，最多显示30个字符
    let displayText = text;
    if (text.length > 30) {
      displayText = text.substring(0, 30) + '...';
    }

    setCopySuccess({
      show: true,
      message: displayText,
      id
    });

    // 3秒后自动隐藏
    setTimeout(() => {
      setCopySuccess(prev => prev.id === id ? { show: false, message: '', id: 0 } : prev);
    }, 3000);
  };

  const copyResult = async () => {
    if (!result) {
      showCopySuccess('没有结果可复制！');
      return;
    }

    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(result);
        showCopySuccess(result);
      } else {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = result;
        document.body.appendChild(textArea);
        textArea.select();
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
          showCopySuccess(result);
        } else {
          showCopySuccess('复制失败，请手动复制');
        }
      }
    } catch {
      showCopySuccess('复制失败，请手动复制');
    }
  };

  return (
    <div className="py-6">
      {/* 页面头部 */}
      <div className="mb-6">
        {/* 面包屑导航 */}
        <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-4">
          <Link href="/" className="hover:text-gray-700">首页</Link>
          <span>/</span>
          <Link href="/tools" className="hover:text-gray-700">工具</Link>
          <span>/</span>
          <span className="text-gray-900">大小写转换</span>
        </nav>

        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">🔤 大小写转换</h1>
          <div className="flex space-x-2">
            <Link
              href="/tools"
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              🏠 返回工具列表
            </Link>
          </div>
        </div>
      </div>

      {/* 大小写转换工具 */}
      <Card>
        <CardContent className="p-6">
          <h2 className="text-xl font-semibold mb-2">文本大小写转换</h2>
          <p className="text-gray-600 text-sm mb-4">在不同的大小写格式之间转换文本</p>

          <div className="space-y-4">
            {/* 输入区域 */}
            <div>
              <label htmlFor="text-input" className="block text-sm font-medium text-gray-700 mb-2">
                输入文本：
              </label>
              <textarea
                id="text-input"
                value={textInput}
                onChange={(e) => setTextInput(e.target.value)}
                placeholder="在这里输入您的文本..."
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical"
              />
            </div>

            {/* 转换按钮 */}
            <div className="flex flex-wrap gap-2">
              <Button
                onClick={() => convertCase('upper')}
                className="bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300"
              >
                UPPERCASE
              </Button>
              <Button
                onClick={() => convertCase('lower')}
                className="bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300"
              >
                lowercase
              </Button>
              <Button
                onClick={() => convertCase('title')}
                className="bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300"
              >
                Title Case
              </Button>
              <Button
                onClick={() => convertCase('camel')}
                className="bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300"
              >
                camelCase
              </Button>
              <Button
                onClick={() => convertCase('pascal')}
                className="bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300"
              >
                PascalCase
              </Button>
              <Button
                onClick={() => convertCase('snake')}
                className="bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300"
              >
                snake_case
              </Button>
              <Button
                onClick={() => convertCase('kebab')}
                className="bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300"
              >
                kebab-case
              </Button>
            </div>

            {/* 结果区域 */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label htmlFor="result-output" className="block text-sm font-medium text-gray-700">
                  转换结果：
                </label>
                {result && currentCaseType && (
                  <ShareButton
                    toolId="case-converter"
                    toolName="大小写转换"
                    input={textInput}
                    output={result}
                    options={{
                      caseType: currentCaseType,
                      wordCount: textInput.split(/\s+/).length,
                      charCount: textInput.length,
                      outputLength: result.length,
                    }}
                    size="sm"
                    showText={false}
                  />
                )}
              </div>
              <div className="relative">
                <textarea
                  id="result-output"
                  value={result}
                  readOnly
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 resize-vertical"
                />
                <Button
                  onClick={copyResult}
                  disabled={!result}
                  className="absolute top-2 right-2 bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 text-xs disabled:opacity-50"
                >
                  📋 复制结果
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Card className="mt-6">
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold mb-3">转换格式说明</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">基础格式</h4>
              <ul className="space-y-1">
                <li><strong>UPPERCASE:</strong> 全部大写</li>
                <li><strong>lowercase:</strong> 全部小写</li>
                <li><strong>Title Case:</strong> 每个单词首字母大写</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">编程格式</h4>
              <ul className="space-y-1">
                <li><strong>camelCase:</strong> 驼峰命名法</li>
                <li><strong>PascalCase:</strong> 帕斯卡命名法</li>
                <li><strong>snake_case:</strong> 下划线命名法</li>
                <li><strong>kebab-case:</strong> 短横线命名法</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 返回按钮 */}
      <div className="mt-8 text-center">
        <Link
          href="/tools"
          className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
        >
          ← 返回工具列表
        </Link>
      </div>

      {/* 复制成功飘字提示 */}
      {copySuccess.show && (
        <div
          className="fixed top-20 left-1/2 transform -translate-x-1/2 z-50 transition-all duration-500 ease-out max-w-xs"
          style={{
            animation: 'copySuccess 3s ease-out forwards'
          }}
        >
          <div className="bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2">
            <span className="text-lg">✓</span>
            <span className="font-mono text-sm break-all">{copySuccess.message}</span>
          </div>
        </div>
      )}

      {/* 添加自定义动画样式 */}
      <style jsx>{`
        @keyframes copySuccess {
          0% {
            opacity: 0;
            transform: translateX(-50%) translateY(-20px) scale(0.8);
          }
          20% {
            opacity: 1;
            transform: translateX(-50%) translateY(0px) scale(1.1);
          }
          40% {
            transform: translateX(-50%) translateY(0px) scale(1);
          }
          80% {
            opacity: 1;
            transform: translateX(-50%) translateY(0px) scale(1);
          }
          100% {
            opacity: 0;
            transform: translateX(-50%) translateY(-10px) scale(0.9);
          }
        }
      `}</style>
    </div>
  );
}
