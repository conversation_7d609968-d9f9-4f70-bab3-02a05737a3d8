'use client';

/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from 'react';
import Link from 'next/link';
import { useSession, signOut } from 'next-auth/react';
import { Button } from '@/components/ui';
import { QuickSearchButton } from '@/components/search';
import { cn } from '@/lib/utils';

export interface HeaderProps {
  className?: string;
}

const Header: React.FC<HeaderProps> = ({ className }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { data: session, status } = useSession();

  return (
    <header className={cn('bg-white border-b border-gray-200 sticky top-0 z-40', className)}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">T</span>
              </div>
              <span className="text-xl font-bold text-gray-900">Tool List</span>
            </Link>
          </div>

          {/* Desktop Navigation - 分层响应式设计 */}
          {/* 主要导航 - 在中等屏幕上显示 */}
          <nav className="hidden lg:flex items-center space-x-6">
            <Link
              href="/"
              className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            >
              首页
            </Link>
            <Link
              href="/tools"
              className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            >
              所有工具
            </Link>

            {/* 工具下拉菜单 */}
            <div className="relative group">
              <button className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center">
                常用工具
                <svg className="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {/* 不可见的桥接区域，防止鼠标移动时菜单消失 */}
              <div className="absolute left-0 top-full w-48 h-2 bg-transparent group-hover:block hidden"></div>

              <div className="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                <div className="py-1">
                  <Link
                    href="/tools/timestamp"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    🕒 时间戳转换
                  </Link>
                  <Link
                    href="/tools/json-formatter"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    📋 JSON格式化
                  </Link>
                  <Link
                    href="/tools/text-converter"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    📝 文本转换
                  </Link>
                  <Link
                    href="/tools/case-converter"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    🔤 大小写转换
                  </Link>
                  <Link
                    href="/tools/ip-converter"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    🌐 IP地址转换
                  </Link>
                  <Link
                    href="/tools/sha-hash"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    🔐 SHA哈希计算
                  </Link>
                  <Link
                    href="/tools/url-encoder"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    🔗 URL编码解码
                  </Link>
                  <Link
                    href="/tools/color-converter"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    🎨 颜色转换
                  </Link>
                  <Link
                    href="/tools/qr-generator"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    📱 二维码生成
                  </Link>
                </div>
              </div>
            </div>

            <Link
              href="/about"
              className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            >
              关于
            </Link>
          </nav>

          {/* 简化导航 - 在中等屏幕上显示 */}
          <nav className="hidden md:flex lg:hidden items-center space-x-4">
            <Link
              href="/tools"
              className="text-gray-600 hover:text-gray-900 px-2 py-2 rounded-md text-sm font-medium transition-colors"
            >
              工具
            </Link>

            {/* 更多菜单 */}
            <div className="relative group">
              <button className="text-gray-600 hover:text-gray-900 px-2 py-2 rounded-md text-sm font-medium transition-colors flex items-center">
                更多
                <svg className="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                <div className="py-1">
                  <Link
                    href="/navigation"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    网站导航
                  </Link>
                  <Link
                    href="/feedback"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    反馈建议
                  </Link>
                  <Link
                    href="/history"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    使用历史
                  </Link>
                  <Link
                    href="/about"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    关于
                  </Link>
                </div>
              </div>
            </div>
          </nav>

          {/* Search Bar - 响应式调整 */}
          <div className="hidden lg:flex flex-1 max-w-md mx-6">
            <QuickSearchButton className="w-full" />
          </div>

          {/* 中等屏幕的搜索按钮 */}
          <div className="hidden md:flex lg:hidden">
            <QuickSearchButton variant="button" className="px-2" />
          </div>

          {/* User Menu - 响应式调整 */}
          <div className="hidden md:flex items-center space-x-2 lg:space-x-4">
            {status === 'loading' ? (
              <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse"></div>
            ) : session ? (
              // 已登录用户菜单
              <div className="relative group">
                <button className="flex items-center space-x-1 lg:space-x-2 text-gray-700 hover:text-gray-900 px-2 lg:px-3 py-2 rounded-md text-sm font-medium">
                  <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <span className="text-primary-600 font-medium">
                      {session.user?.name?.charAt(0) || session.user?.email?.charAt(0)}
                    </span>
                  </div>
                  <span className="hidden xl:block max-w-24 truncate">{session.user?.name || session.user?.email}</span>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {/* 不可见的桥接区域，防止鼠标移动时菜单消失 */}
                <div className="absolute right-0 top-full w-48 h-2 bg-transparent group-hover:block hidden"></div>

                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                  <div className="py-1">
                    <div className="px-4 py-2 text-sm text-gray-500 border-b">
                      {session.user?.email}
                      {(session.user as any)?.role === 'admin' && (
                        <span className="ml-2 text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">
                          管理员
                        </span>
                      )}
                    </div>
                    <Link
                      href="/profile"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      个人资料
                    </Link>
                    <Link
                      href="/favorites"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      收藏工具
                    </Link>
                    {(session.user as any)?.role === 'admin' && (
                      <Link
                        href="/admin/database"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        数据库管理
                      </Link>
                    )}
                    <button
                      onClick={() => signOut()}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      退出登录
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              // 未登录用户按钮 - 响应式调整
              <>
                <Button variant="ghost" size="sm" className="px-2 lg:px-4" asChild>
                  <Link href="/login">登录</Link>
                </Button>
                <Button variant="primary" size="sm" className="px-2 lg:px-4" asChild>
                  <Link href="/register">注册</Link>
                </Button>
              </>
            )}
          </div>

          {/* Mobile menu button - 在小屏幕和中等屏幕都显示 */}
          <div className="md:hidden flex items-center space-x-2">
            {/* 中小屏幕的搜索按钮 */}
            <QuickSearchButton variant="button" className="p-2" />

            {/* 汉堡菜单按钮 */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary-500 p-2 rounded-md"
              aria-label="打开菜单"
            >
              <svg
                className="h-6 w-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                {isMenuOpen ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-gray-200">
            <div className="px-2 pt-2 pb-3 space-y-1">
              {/* Search */}
              <div className="px-3 py-2">
                <QuickSearchButton className="w-full" />
              </div>

              {/* Navigation Links */}
              <Link
                href="/"
                className="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                onClick={() => setIsMenuOpen(false)}
              >
                首页
              </Link>
              <Link
                href="/tools"
                className="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                onClick={() => setIsMenuOpen(false)}
              >
                所有工具
              </Link>

              {/* 常用工具 */}
              <div className="px-3 py-2">
                <div className="text-sm font-medium text-gray-500 mb-2">常用工具</div>
                <div className="space-y-1 ml-4">
                  <Link
                    href="/tools/timestamp"
                    className="block px-3 py-2 rounded-md text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    🕒 时间戳转换
                  </Link>
                  <Link
                    href="/tools/json-formatter"
                    className="block px-3 py-2 rounded-md text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    📋 JSON格式化
                  </Link>
                  <Link
                    href="/tools/text-converter"
                    className="block px-3 py-2 rounded-md text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    📝 文本转换
                  </Link>
                </div>
              </div>

              <Link
                href="/about"
                className="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                onClick={() => setIsMenuOpen(false)}
              >
                关于
              </Link>

              {/* User Actions */}
              <div className="px-3 py-2 space-y-2">
                {session ? (
                  // 已登录用户
                  <>
                    <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                      <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                        <span className="text-primary-600 font-medium">
                          {session.user?.name?.charAt(0) || session.user?.email?.charAt(0)}
                        </span>
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">{session.user?.name || session.user?.email}</div>
                        <div className="text-sm text-gray-500">{session.user?.email}</div>
                        {(session.user as any)?.role === 'admin' && (
                          <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">
                            管理员
                          </span>
                        )}
                      </div>
                    </div>

                    <Link
                      href="/profile"
                      className="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      个人资料
                    </Link>
                    <Link
                      href="/favorites"
                      className="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      收藏工具
                    </Link>
                    {(session.user as any)?.role === 'admin' && (
                      <Link
                        href="/admin/database"
                        className="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        数据库管理
                      </Link>
                    )}
                    <button
                      onClick={() => {
                        signOut();
                        setIsMenuOpen(false);
                      }}
                      className="block w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                    >
                      退出登录
                    </button>
                  </>
                ) : (
                  // 未登录用户
                  <>
                    <Button variant="ghost" size="sm" className="w-full justify-start" asChild>
                      <Link href="/login" onClick={() => setIsMenuOpen(false)}>登录</Link>
                    </Button>
                    <Button variant="primary" size="sm" className="w-full" asChild>
                      <Link href="/register" onClick={() => setIsMenuOpen(false)}>注册</Link>
                    </Button>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
