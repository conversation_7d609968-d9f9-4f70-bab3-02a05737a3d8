import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db/mongodb';
import User from '@/lib/db/models/User';

export async function GET(_request: NextRequest) {
  try {
    console.log('测试数据库连接...');
    
    // 连接数据库
    await connectDB();
    console.log('数据库连接成功');

    // 测试查询
    const userCount = await User.countDocuments();
    console.log('用户数量:', userCount);

    // 查找演示用户
    const demoUsers = await User.find({
      email: { $in: ['<EMAIL>', '<EMAIL>'] }
    }).select('-password');

    return NextResponse.json({
      success: true,
      message: '数据库连接正常',
      data: {
        userCount,
        demoUsers,
        mongoUri: process.env.MONGODB_URI ? '已配置' : '未配置',
        nodeEnv: process.env.NODE_ENV,
        nextauthUrl: process.env.NEXTAUTH_URL,
        nextauthSecret: process.env.NEXTAUTH_SECRET ? '已配置' : '未配置',
      }
    });

  } catch (error) {
    console.error('数据库测试失败:', error);
    
    return NextResponse.json({
      success: false,
      message: '数据库连接失败',
      error: error instanceof Error ? error.message : '未知错误',
      mongoUri: process.env.MONGODB_URI ? '已配置' : '未配置',
      nodeEnv: process.env.NODE_ENV,
    }, { status: 500 });
  }
}
