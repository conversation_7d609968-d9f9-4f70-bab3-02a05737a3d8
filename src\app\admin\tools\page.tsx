'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Wrench,
  RefreshCw,
  RotateCcw,
  Database,
  CheckCircle,
  XCircle,
  Info,
  AlertTriangle
} from 'lucide-react';

interface ToolSyncData {
  database: number;
  constants: number;
  needSync: boolean;
}

interface SyncResult {
  total: number;
  created: number;
  updated: number;
  skipped: number;
  errors: Array<{ tool: string; error: string }>;
}

interface ApiResponse {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

export default function ToolsManagePage() {
  const [syncData, setSyncData] = useState<ToolSyncData | null>(null);
  const [syncResult, setSyncResult] = useState<SyncResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'warning' | 'info'; text: string } | null>(null);

  // 获取同步状态
  const getSyncStatus = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/tools/sync?action=count');
      const data: ApiResponse = await response.json();
      
      if (data.success) {
        setSyncData(data.data);
        setMessage({ 
          type: data.data.needSync ? 'warning' : 'success', 
          text: data.data.needSync ? '工具数据需要同步' : '工具数据已同步' 
        });
      } else {
        setMessage({ type: 'error', text: data.message || '获取同步状态失败' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: '获取同步状态失败' });
    } finally {
      setLoading(false);
    }
  };

  // 同步工具数据
  const syncTools = async (force = false) => {
    if (!force && !confirm('确定要同步工具数据到数据库吗？')) {
      return;
    }

    setLoading(true);
    setSyncResult(null);
    
    try {
      const response = await fetch('/api/tools/sync', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'sync', force }),
      });
      
      const data: ApiResponse = await response.json();
      
      if (data.success) {
        setSyncResult(data.data);
        setMessage({ type: 'success', text: '工具数据同步成功' });
        await getSyncStatus(); // 刷新状态
      } else {
        setMessage({ type: 'error', text: data.message || '同步失败' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: '同步失败' });
    } finally {
      setLoading(false);
    }
  };

  // 强制同步（覆盖现有数据）
  const forceSyncTools = async () => {
    if (!confirm('确定要强制同步吗？这将覆盖数据库中的现有工具数据。')) {
      return;
    }
    
    await syncTools(true);
  };

  // 页面加载时获取状态
  useEffect(() => {
    getSyncStatus();
  }, []);

  // 自动清除消息
  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [message]);

  const getMessageIcon = (type: string) => {
    switch (type) {
      case 'success': return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error': return <XCircle className="w-5 h-5 text-red-500" />;
      case 'warning': return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      default: return <Info className="w-5 h-5 text-blue-500" />;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center gap-3 mb-8">
          <Wrench className="w-8 h-8 text-blue-600" />
          <h1 className="text-3xl font-bold text-gray-900">工具管理</h1>
        </div>

        {/* 消息提示 */}
        {message && (
          <Alert className={`mb-6 ${
            message.type === 'success' ? 'border-green-200 bg-green-50' :
            message.type === 'error' ? 'border-red-200 bg-red-50' :
            message.type === 'warning' ? 'border-yellow-200 bg-yellow-50' :
            'border-blue-200 bg-blue-50'
          }`}>
            <div className="flex items-center gap-2">
              {getMessageIcon(message.type)}
              <AlertDescription>{message.text}</AlertDescription>
            </div>
          </Alert>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 同步状态 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="w-5 h-5" />
                同步状态
              </CardTitle>
            </CardHeader>
            <CardContent>
              {syncData ? (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span>常量文件工具数</span>
                    <Badge variant="outline">{syncData.constants}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>数据库工具数</span>
                    <Badge variant="outline">{syncData.database}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>同步状态</span>
                    <Badge variant={syncData.needSync ? 'destructive' : 'default'}>
                      {syncData.needSync ? '需要同步' : '已同步'}
                    </Badge>
                  </div>
                  
                  <Button 
                    onClick={getSyncStatus} 
                    disabled={loading}
                    variant="outline" 
                    size="sm"
                    className="w-full"
                  >
                    <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                    刷新状态
                  </Button>
                </div>
              ) : (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-sm text-gray-500">正在获取状态...</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 同步结果 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <RotateCcw className="w-5 h-5" />
                同步结果
              </CardTitle>
            </CardHeader>
            <CardContent>
              {syncResult ? (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span>总工具数</span>
                    <Badge variant="outline">{syncResult.total}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>新创建</span>
                    <Badge variant="default">{syncResult.created}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>已更新</span>
                    <Badge variant="secondary">{syncResult.updated}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>已跳过</span>
                    <Badge variant="outline">{syncResult.skipped}</Badge>
                  </div>
                  {syncResult.errors.length > 0 && (
                    <div className="flex items-center justify-between">
                      <span>错误数</span>
                      <Badge variant="destructive">{syncResult.errors.length}</Badge>
                    </div>
                  )}
                  
                  {syncResult.errors.length > 0 && (
                    <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                      <h4 className="text-sm font-medium text-red-800 mb-2">同步错误</h4>
                      <div className="space-y-1">
                        {syncResult.errors.map((error, index) => (
                          <div key={index} className="text-xs text-red-700">
                            <strong>{error.tool}</strong>: {error.error}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-sm text-gray-500">暂无同步结果</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 同步操作 */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>同步操作</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button 
                onClick={() => syncTools(false)}
                disabled={loading}
                className="flex items-center gap-2"
              >
                <RotateCcw className="w-4 h-4" />
                同步工具数据
              </Button>
              
              <Button 
                onClick={forceSyncTools}
                disabled={loading}
                variant="outline"
                className="flex items-center gap-2"
              >
                <RefreshCw className="w-4 h-4" />
                强制同步（覆盖）
              </Button>
            </div>
            
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-2">操作说明</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• <strong>同步工具数据</strong>：将常量文件中的工具数据同步到数据库，跳过已存在的工具</li>
                <li>• <strong>强制同步</strong>：覆盖数据库中的现有工具数据，更新所有字段</li>
                <li>• 同步操作会保留工具的访问统计数据</li>
                <li>• 建议在添加新工具或修改工具信息后执行同步</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
