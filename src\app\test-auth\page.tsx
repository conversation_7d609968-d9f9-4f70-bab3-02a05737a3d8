'use client';

import { useSession, signIn, signOut } from 'next-auth/react';

export default function TestAuth() {
  const { data: session, status } = useSession();

  if (status === 'loading') {
    return <div className="p-8">Loading...</div>;
  }

  return (
    <div className="p-8 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">NextAuth 测试页面</h1>
      
      <div className="bg-gray-100 p-4 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-2">会话状态</h2>
        <p><strong>Status:</strong> {status}</p>
        <p><strong>当前URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'N/A'}</p>
      </div>

      {session ? (
        <div className="space-y-4">
          <div className="bg-green-100 p-4 rounded-lg">
            <h2 className="text-lg font-semibold mb-2 text-green-800">已登录</h2>
            <p><strong>邮箱:</strong> {session.user?.email}</p>
            <p><strong>姓名:</strong> {session.user?.name}</p>
            <p><strong>角色:</strong> {(session.user as any)?.role}</p>
            <p><strong>ID:</strong> {(session.user as any)?.id}</p>
          </div>
          
          <button
            onClick={() => signOut()}
            className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
          >
            退出登录
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="bg-yellow-100 p-4 rounded-lg">
            <h2 className="text-lg font-semibold mb-2 text-yellow-800">未登录</h2>
            <p>请使用以下方式登录：</p>
          </div>
          
          <div className="space-y-2">
            <button
              onClick={() => signIn('credentials', { 
                email: '<EMAIL>', 
                password: 'admin123',
                redirect: false 
              })}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 mr-2"
            >
              演示管理员登录
            </button>
            
            <button
              onClick={() => signIn('credentials', { 
                email: '<EMAIL>', 
                password: 'user123',
                redirect: false 
              })}
              className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 mr-2"
            >
              演示用户登录
            </button>
            
            <button
              onClick={() => signIn()}
              className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
            >
              跳转到登录页
            </button>
          </div>
        </div>
      )}

      <div className="mt-8 bg-blue-100 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-2 text-blue-800">调试信息</h2>
        <pre className="text-sm bg-white p-2 rounded overflow-auto">
          {JSON.stringify({ session, status }, null, 2)}
        </pre>
      </div>

      <div className="mt-4">
        <a href="/" className="text-blue-600 hover:underline">← 返回首页</a>
      </div>
    </div>
  );
}
