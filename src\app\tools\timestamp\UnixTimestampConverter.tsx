'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { ToolComponentProps } from '@/types/tools';
import ShareButton from '@/components/share/ShareButton';

// 完整的多语言翻译（基于参考实现）
const translations = {
  'en-US': {
    pageTitle: 'Unix Timestamp Converter',
    currentSecondsLabel: 'Current Timestamp (s)',
    currentMillisecondsLabel: 'Current Timestamp (ms)',
    currentTimeLabel: 'Current Time',
    timestampConversionTitle: 'Timestamp Conversion',
    timestampConversionDesc: 'Supports seconds, milliseconds, microseconds and nanoseconds',
    timestampInputPlaceholder: 'Enter timestamp',
    formatLabel: 'Format',
    gmtLabel: 'GMT',
    localTimezoneLabel: 'Local Timezone',
    relativeTimeLabel: 'Relative Time',
    dateConversionTitle: 'Date Conversion',
    dateInputPlaceholder: 'YYYY-MM-DD HH:mm:ss',
    secondsLabel: 'Timestamp (s)',
    millisecondsLabel: 'Timestamp (ms)',
    dateGmtLabel: 'GMT',
    dateLocalLabel: 'Local Timezone',
    dateRelativeLabel: 'Relative Time',
    aboutTitle: 'About Unix Timestamp',
    whatIsUnixTitle: 'What is Unix Timestamp?',
    whatIsUnixDesc: 'Unix timestamp is an integer representing the number of seconds elapsed since January 1, 1970 00:00:00 UTC (the Unix Epoch).',
    timeRangeTitle: 'Time Range',
    timeRangeStart: '- Start time: January 1, 1970 00:00:00 UTC, timestamp: 0',
    timeRangeEnd: '- End time: January 19, 2038 03:14:07 UTC, timestamp: 2,147,483,647',
    timeRangeNote: '* Note: This limitation is based on 32-bit systems. 64-bit systems can represent ±292,277,026,596 years.',
    commonUnitsTitle: 'Common Units',
    unitSeconds: 'Seconds: Most commonly used, 10 digits',
    unitMilliseconds: 'Milliseconds: 1/1000 of a second, 13 digits',
    unitMicroseconds: 'Microseconds: 1/1,000,000 of a second, 16 digits',
    unitNanoseconds: 'Nanoseconds: 1/1,000,000,000 of a second, 19 digits',
    whyUseTitle: 'Why Use Timestamps?',
    whyUse1: 'Unified standard: Not affected by time zones',
    whyUse2: 'Easy calculation: Can be directly compared',
    whyUse3: 'Storage efficient: Represents complete date and time with a single number',
    whyUse4: 'Cross-platform: Supported by all mainstream programming languages',
    y2038Title: 'Year 2038 Problem',
    y2038Desc: 'On 32-bit systems, Unix timestamp will reach its maximum value of 2,147,483,647 on January 19, 2038 03:14:07 UTC, potentially causing overflow issues. Modern 64-bit systems are not affected by this limitation.',
    footerText: '© 2025 Unix Timestamp Converter - A useful tool for developers and system administrators.',
    formatSeconds: 'Seconds (10 digits)',
    formatMilliseconds: 'Milliseconds (13 digits)',
    formatMicroseconds: 'Microseconds (16 digits)',
    formatNanoseconds: 'Nanoseconds (19 digits)',
    formatError: 'Invalid timestamp format',
    oneMinLater: '1 min later',
    threeMinLater: '3 min later',
    fiveMinLater: '5 min later',
    customMinLater: 'min later',
    customCopyBtn: 'Copy',
    customPlaceholder: 'Custom',
    pcHint: '💡 Tip: Double-click copy buttons for quick copy',
    mobileHint: '💡 Tip: Long press copy buttons for quick copy',
    featuresBtn: '✨ Features',
    caseConverterBtn: '🔤 Case Converter'
  },
  'zh-CN': {
    pageTitle: 'Unix 时间戳转换器',
    currentSecondsLabel: '当前时间戳（秒）',
    currentMillisecondsLabel: '当前时间戳（毫秒）',
    currentTimeLabel: '当前时间',
    timestampConversionTitle: '时间戳转换',
    timestampConversionDesc: '支持秒、毫秒、微秒和纳秒的时间戳',
    timestampInputPlaceholder: '输入时间戳',
    formatLabel: '格式',
    gmtLabel: '格林威治标准时间',
    localTimezoneLabel: '本地时区',
    relativeTimeLabel: '相对当前时间',
    dateConversionTitle: '日期转换',
    dateInputPlaceholder: 'YYYY-MM-DD HH:mm:ss / 年-月-日 时:分:秒',
    secondsLabel: '时间戳（秒）',
    millisecondsLabel: '时间戳（毫秒）',
    dateGmtLabel: '格林威治标准时间',
    dateLocalLabel: '本地时区',
    dateRelativeLabel: '相对当前时间',
    aboutTitle: '关于 Unix 时间戳',
    whatIsUnixTitle: '什么是 Unix 时间戳?',
    whatIsUnixDesc: 'Unix 时间戳是一个整数，表示自 1970 年 1 月 1 日 00:00:00 UTC（协调世界时）以来经过的秒数，称为 Unix 纪元或 POSIX 时间。',
    timeRangeTitle: '时间范围',
    timeRangeStart: '- 开始时间: 1970年1月1日 00:00:00 UTC，时间戳: 0',
    timeRangeEnd: '- 结束时间: 2038年1月19日 03:14:07 UTC，时间戳: 2,147,483,647',
    timeRangeNote: '* 注意: 在基于32位系统的限制，64位系统可表示范围为±292,277,026,596年',
    commonUnitsTitle: '常用单位',
    unitSeconds: '秒: 最常用单位，10位数字',
    unitMilliseconds: '毫秒: 千分之一秒，13位数字',
    unitMicroseconds: '微秒: 百万分之一秒，16位数字',
    unitNanoseconds: '纳秒: 十亿分之一秒，19位数字',
    whyUseTitle: '为何使用时间戳',
    whyUse1: '统一标准: 不受时区影响',
    whyUse2: '易计算: 可直接比较大小',
    whyUse3: '存储高效: 用单个数字表示完整日期时间',
    whyUse4: '跨平台: 所有主流编程语言均均支持',
    y2038Title: '2038年问题',
    y2038Desc: '在32位系统上，Unix时间戳将在2038年1月19日03:14:07 UTC达到最大值2,147,483,647，可能导致溢出问题，现代64位系统不受此限制影响。',
    footerText: '© 2025 Unix 时间戳转换器 - 为开发者和系统管理员提供的实用工具。',
    formatSeconds: '秒（10位数字）',
    formatMilliseconds: '毫秒（13位数字）',
    formatMicroseconds: '微秒（16位数字）',
    formatNanoseconds: '纳秒（19位数字）',
    formatError: '无效的时间戳格式',
    oneMinLater: '1分钟后',
    threeMinLater: '3分钟后',
    fiveMinLater: '5分钟后',
    customMinLater: '分钟后',
    customCopyBtn: '复制',
    customPlaceholder: '自定义',
    pcHint: '💡 提示：双击复制3分钟后时间戳',
    mobileHint: '💡 提示：双击复制3分钟后时间戳',
    featuresBtn: '✨ 功能特性',
    caseConverterBtn: '🔤 大小写转换'
  }
};

type UnixTimestampConverterProps = ToolComponentProps;

const UnixTimestampConverter: React.FC<UnixTimestampConverterProps> = ({
  onUsage
}) => {
  const [currentLang] = useState<'en-US' | 'zh-CN'>('zh-CN');
  const [currentTimestamp, setCurrentTimestamp] = useState<number>(0);
  const [currentMilliseconds, setCurrentMilliseconds] = useState<number>(0);
  const [currentDatetime, setCurrentDatetime] = useState<string>('');
  const [timestampInput, setTimestampInput] = useState<string>('');
  const [dateInput, setDateInput] = useState<string>('');
  const [isClient, setIsClient] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // 右键菜单状态
  const [contextMenu, setContextMenu] = useState<{
    visible: boolean;
    x: number;
    y: number;
    type: string;
  }>({
    visible: false,
    x: 0,
    y: 0,
    type: ''
  });

  // 自定义分钟数状态
  const [customMinutes, setCustomMinutes] = useState<{[key: string]: string}>({
    seconds: '',
    milliseconds: '',
    datetime: ''
  });

  // 复制成功提示状态
  const [copySuccess, setCopySuccess] = useState<{
    show: boolean;
    message: string;
    id: number;
  }>({
    show: false,
    message: '',
    id: 0
  });

  // 转换结果状态
  const [timestampFormat, setTimestampFormat] = useState<string>('');
  const [timestampGmt, setTimestampGmt] = useState<string>('');
  const [timestampLocal, setTimestampLocal] = useState<string>('');
  const [timestampRelative, setTimestampRelative] = useState<string>('');

  const [dateTimestampSeconds, setDateTimestampSeconds] = useState<string>('');
  const [dateTimestampMilliseconds, setDateTimestampMilliseconds] = useState<string>('');
  const [dateGmt, setDateGmt] = useState<string>('');
  const [dateLocal, setDateLocal] = useState<string>('');
  const [dateRelative, setDateRelative] = useState<string>('');

  const updateTimer = useRef<NodeJS.Timeout | null>(null);

  // 客户端水合完成后初始化
  useEffect(() => {
    setIsClient(true);
    setIsMobile(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent));
    updateCurrentTime();

    // 设置默认值
    const now = new Date();
    const currentTimestamp = Math.floor(now.getTime() / 1000);
    const currentDatetime = formatDateForInput(now);

    setTimestampInput(currentTimestamp.toString());
    setDateInput(currentDatetime);

    const timer = setInterval(updateCurrentTime, 1000);
    updateTimer.current = timer;

    return () => {
      if (updateTimer.current) {
        clearInterval(updateTimer.current);
      }
    };
  }, []);

  const lang = translations[currentLang];

  // 格式化日期为输入框格式 (YYYY-MM-DDTHH:mm:ss)
  const formatDateForInput = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
  };

  // 更新当前时间
  const updateCurrentTime = () => {
    const now = new Date();
    setCurrentTimestamp(Math.floor(now.getTime() / 1000));
    setCurrentMilliseconds(now.getTime());
    setCurrentDatetime(now.toLocaleString());
  };

  // 格式化相对时间（精确到秒）
  const formatRelativeTime = useCallback((date: Date): string => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const totalSeconds = Math.floor(Math.abs(diffMs) / 1000);

    const isInFuture = diffMs < 0;
    const prefix = isInFuture ? '未来' : '过去';

    // 计算各个时间单位
    const years = Math.floor(totalSeconds / (365 * 24 * 3600));
    const months = Math.floor((totalSeconds % (365 * 24 * 3600)) / (30 * 24 * 3600));
    const days = Math.floor((totalSeconds % (30 * 24 * 3600)) / (24 * 3600));
    const hours = Math.floor((totalSeconds % (24 * 3600)) / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    // 构建时间差字符串
    const parts: string[] = [];

    if (years > 0) {
      parts.push(`${years}年`);
    }
    if (months > 0) {
      parts.push(`${months}个月`);
    }
    if (days > 0) {
      parts.push(`${days}天`);
    }
    if (hours > 0) {
      parts.push(`${hours}小时`);
    }
    if (minutes > 0) {
      parts.push(`${minutes}分钟`);
    }
    if (seconds > 0 || parts.length === 0) {
      parts.push(`${seconds}秒`);
    }

    // 根据时间长度决定显示的详细程度
    let result: string;
    if (years > 0) {
      // 超过1年，显示年月
      result = parts.slice(0, 2).join('');
    } else if (months > 0) {
      // 超过1个月，显示月天
      result = parts.slice(0, 2).join('');
    } else if (days > 0) {
      // 超过1天，显示天时分
      result = parts.slice(0, 3).join('');
    } else if (hours > 0) {
      // 超过1小时，显示时分秒
      result = parts.slice(0, 3).join('');
    } else {
      // 1小时内，显示分秒
      result = parts.slice(-2).join('');
    }

    return `${prefix} ${result}`;
  }, []);

  // 检测时间戳格式
  const detectTimestampFormat = useCallback((input: string): string => {
    const length = input.length;
    if (length <= 10) {
      return lang.formatSeconds;
    } else if (length <= 13) {
      return lang.formatMilliseconds;
    } else if (length <= 16) {
      return lang.formatMicroseconds;
    } else if (length <= 19) {
      return lang.formatNanoseconds;
    } else {
      return lang.formatError;
    }
  }, [lang]);

  // 转换时间戳
  const convertTimestamp = useCallback((input: string) => {
    if (!input.trim()) {
      setTimestampFormat('');
      setTimestampGmt('');
      setTimestampLocal('');
      setTimestampRelative('');
      return;
    }

    const timestamp = parseFloat(input);
    if (isNaN(timestamp)) {
      setTimestampFormat(lang.formatError);
      setTimestampGmt('');
      setTimestampLocal('');
      setTimestampRelative('');
      return;
    }

    let date: Date;
    const length = input.length;

    if (length <= 10) {
      // 秒
      date = new Date(timestamp * 1000);
    } else if (length <= 13) {
      // 毫秒
      date = new Date(timestamp);
    } else if (length <= 16) {
      // 微秒
      date = new Date(timestamp / 1000);
    } else {
      // 纳秒
      date = new Date(timestamp / 1000000);
    }

    if (isNaN(date.getTime())) {
      setTimestampFormat(lang.formatError);
      setTimestampGmt('');
      setTimestampLocal('');
      setTimestampRelative('');
      return;
    }

    setTimestampFormat(detectTimestampFormat(input));
    setTimestampGmt(date.toUTCString());
    setTimestampLocal(date.toLocaleString());
    setTimestampRelative(formatRelativeTime(date));
  }, [lang, detectTimestampFormat, formatRelativeTime]);

  // 转换日期
  const convertDate = useCallback((input: string) => {
    if (!input.trim()) {
      setDateTimestampSeconds('');
      setDateTimestampMilliseconds('');
      setDateGmt('');
      setDateLocal('');
      setDateRelative('');
      return;
    }

    const date = new Date(input);
    if (isNaN(date.getTime())) {
      setDateTimestampSeconds('');
      setDateTimestampMilliseconds('');
      setDateGmt('');
      setDateLocal('');
      setDateRelative('');
      return;
    }

    const timestampS = Math.floor(date.getTime() / 1000);
    const timestampMs = date.getTime();

    setDateTimestampSeconds(timestampS.toString());
    setDateTimestampMilliseconds(timestampMs.toString());
    setDateGmt(date.toUTCString());
    setDateLocal(date.toLocaleString());
    setDateRelative(formatRelativeTime(date));
  }, [formatRelativeTime]);

  // 显示复制成功提示
  const showCopySuccess = (text: string) => {
    const id = Date.now();

    // 截断长文本，最多显示30个字符
    let displayText = text;
    if (text.length > 30) {
      displayText = text.substring(0, 30) + '...';
    }

    // 如果当前已经有提示在显示，先隐藏它
    setCopySuccess(() => ({ show: false, message: '', id: 0 }));

    // 短暂延迟后显示新提示，确保动画重新开始
    setTimeout(() => {
      setCopySuccess({
        show: true,
        message: displayText,
        id
      });
    }, 50);

    // 3秒后自动隐藏
    setTimeout(() => {
      setCopySuccess(prev => prev.id === id ? { show: false, message: '', id: 0 } : prev);
    }, 3050);
  };

  // 复制到剪贴板
  const copyToClipboard = async (text: string) => {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text);
        showCopySuccess(text);
        onUsage?.('copy', { text: text.substring(0, 50) });
      } else {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
          showCopySuccess(text);
          onUsage?.('copy', { text: text.substring(0, 50) });
        } else {
          showCopySuccess('复制失败，请手动复制');
        }
      }
    } catch (err) {
      console.error('复制失败:', err);
      showCopySuccess('复制失败，请手动复制');
    }
  };

  // 处理快捷复制
  const handleQuickCopy = (type: string, minutes: number) => {
    const now = Date.now();
    const futureTimestamp = Math.floor(now / 1000) + (minutes * 60);
    let value: string;

    if (type === 'milliseconds') {
      value = (futureTimestamp * 1000).toString();
    } else if (type === 'datetime') {
      value = new Date(futureTimestamp * 1000).toLocaleString();
    } else {
      value = futureTimestamp.toString();
    }

    copyToClipboard(value);
  };

  // 处理自定义分钟复制
  const handleCustomCopy = (type: string) => {
    const minutes = parseInt(customMinutes[type]) || 5;
    handleQuickCopy(type, minutes);
    setContextMenu({ visible: false, x: 0, y: 0, type: '' });
  };

  // 关闭右键菜单
  const closeContextMenu = () => {
    setContextMenu({ visible: false, x: 0, y: 0, type: '' });
  };

  // 实时转换
  useEffect(() => {
    convertTimestamp(timestampInput);
  }, [timestampInput, lang, convertTimestamp]);

  useEffect(() => {
    convertDate(dateInput);
  }, [dateInput, lang, convertDate]);

  // 实时更新相对时间显示（每秒更新）
  useEffect(() => {
    const updateRelativeTime = () => {
      // 更新时间戳转换的相对时间
      if (timestampInput.trim()) {
        const timestamp = parseFloat(timestampInput);
        if (!isNaN(timestamp)) {
          let date: Date;
          const length = timestampInput.length;

          if (length <= 10) {
            date = new Date(timestamp * 1000);
          } else if (length <= 13) {
            date = new Date(timestamp);
          } else if (length <= 16) {
            date = new Date(timestamp / 1000);
          } else {
            date = new Date(timestamp / 1000000);
          }

          if (!isNaN(date.getTime())) {
            setTimestampRelative(formatRelativeTime(date));
          }
        }
      }

      // 更新日期转换的相对时间
      if (dateInput.trim()) {
        const date = new Date(dateInput);
        if (!isNaN(date.getTime())) {
          setDateRelative(formatRelativeTime(date));
        }
      }
    };

    const interval = setInterval(updateRelativeTime, 1000);
    return () => clearInterval(interval);
  }, [timestampInput, dateInput, formatRelativeTime]);

  // 点击外部关闭右键菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // 检查点击是否在菜单内部
      const target = event.target as Element;
      const menuElement = document.querySelector('[data-context-menu]');

      if (menuElement && menuElement.contains(target)) {
        // 点击在菜单内部，不关闭菜单
        return;
      }

      // 检查是否点击的是复制按钮（避免右键时立即关闭）
      const copyButton = target.closest('button');
      if (copyButton && copyButton.textContent?.includes('📋')) {
        return;
      }

      // 点击在菜单外部，关闭菜单
      closeContextMenu();
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      // ESC 键关闭菜单
      if (event.key === 'Escape') {
        closeContextMenu();
      }
    };

    if (contextMenu.visible) {
      // 延迟添加事件监听器，避免立即触发
      setTimeout(() => {
        document.addEventListener('click', handleClickOutside);
        document.addEventListener('keydown', handleKeyDown);
      }, 100);

      return () => {
        document.removeEventListener('click', handleClickOutside);
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [contextMenu.visible]);



  // 复制按钮组件
  const CopyButton: React.FC<{
    type: string;
    value: string;
  }> = ({ type, value }) => {
    const clickCountRef = useRef(0);
    const clickTimerRef = useRef<NodeJS.Timeout | null>(null);
    const lastClickTimeRef = useRef(0);

    const handleClick = (e: React.MouseEvent) => {
      e.preventDefault();

      const now = Date.now();
      const timeSinceLastClick = now - lastClickTimeRef.current;
      lastClickTimeRef.current = now;

      // 移动端双击检测
      if (isMobile) {
        // 如果距离上次点击时间很短（小于300ms），认为是双击
        if (timeSinceLastClick < 300 && clickCountRef.current === 1) {
          // 双击 - 清除单击定时器，显示快捷菜单
          if (clickTimerRef.current) {
            clearTimeout(clickTimerRef.current);
            clickTimerRef.current = null;
          }
          clickCountRef.current = 0;
          handleContextMenu(e, type);
          return;
        }

        // 单击处理 - 延迟执行，等待可能的双击
        clickCountRef.current = 1;

        // 清除之前的定时器
        if (clickTimerRef.current) {
          clearTimeout(clickTimerRef.current);
        }

        // 延迟执行单击复制，给双击留出时间
        clickTimerRef.current = setTimeout(() => {
          if (clickCountRef.current === 1) {
            // 确认是单击，执行复制
            copyToClipboard(value);
          }
          clickCountRef.current = 0;
          clickTimerRef.current = null;
        }, 300);
      } else {
        // 桌面端直接复制
        copyToClipboard(value);
      }
    };

    const handleContextMenu = (e: React.MouseEvent, buttonType: string) => {
      e.preventDefault();

      // 获取菜单尺寸（估算）
      const menuWidth = 200;
      const menuHeight = 180;

      // 获取视窗尺寸
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // 计算菜单位置，确保不超出屏幕
      let x = e.clientX;
      let y = e.clientY;

      // 如果菜单会超出右边界，向左调整
      if (x + menuWidth > viewportWidth) {
        x = viewportWidth - menuWidth - 10;
      }

      // 如果菜单会超出下边界，向上调整
      if (y + menuHeight > viewportHeight) {
        y = viewportHeight - menuHeight - 10;
      }

      // 确保不超出左边界和上边界
      x = Math.max(10, x);
      y = Math.max(10, y);

      setContextMenu({
        visible: true,
        x,
        y,
        type: buttonType
      });
    };

    return (
      <button
        onClick={handleClick}
        onContextMenu={isMobile ? undefined : (e) => handleContextMenu(e, type)}
        className="bg-blue-500 hover:bg-blue-600 text-white border-none rounded px-3 py-2 cursor-pointer text-base min-w-10 h-9 flex items-center justify-center transition-all duration-150 hover:transform hover:-translate-y-0.5 active:transform active:scale-95"
        title={isMobile ? "单击复制，双击快捷复制" : "单击复制，右键快捷复制"}
      >
        📋
      </button>
    );
  };

  return (
    <div className="w-full max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-5">
      {/* 提示信息 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-5 text-center">
        <span className="text-blue-700 text-sm">
          {isMobile
            ? "💡 提示：单击复制按钮直接复制，双击复制按钮可快捷复制1、3、5分钟后的时间戳"
            : "💡 提示：单击复制按钮直接复制，右键复制按钮可快捷复制1、3、5分钟后的时间戳"
          }
        </span>
      </div>

      {/* 当前时间戳区域 */}
      <div className="mb-8 bg-gray-50 p-4 rounded-lg">
        <div className="flex flex-col gap-3">
          {/* 当前时间戳（秒） */}
          <div className="flex items-center">
            <div className="w-36 text-sm text-gray-600">{lang.currentSecondsLabel}</div>
            <div className="flex-1 text-sm text-gray-800 font-mono">
              {isClient ? currentTimestamp : '加载中...'}
            </div>
            <CopyButton
              type="seconds"
              value={isClient ? currentTimestamp.toString() : ''}
            />
          </div>

          {/* 当前时间戳（毫秒） */}
          <div className="flex items-center">
            <div className="w-36 text-sm text-gray-600">{lang.currentMillisecondsLabel}</div>
            <div className="flex-1 text-sm text-gray-800 font-mono">
              {isClient ? currentMilliseconds : '加载中...'}
            </div>
            <CopyButton
              type="milliseconds"
              value={isClient ? currentMilliseconds.toString() : ''}
            />
          </div>

          {/* 当前时间 */}
          <div className="flex items-center">
            <div className="w-36 text-sm text-gray-600">{lang.currentTimeLabel}</div>
            <div className="flex-1 text-sm text-gray-800 font-mono">
              {isClient ? currentDatetime : '加载中...'}
            </div>
            <CopyButton
              type="datetime"
              value={isClient ? currentDatetime : ''}
            />
          </div>
        </div>

        {/* 分享按钮 */}
        <div className="mt-4 pt-3 border-t border-gray-200 flex justify-center">
          <ShareButton
            toolId="timestamp-converter"
            toolName="Unix 时间戳转换器"
            input={timestampInput || dateInput || '当前时间'}
            output={`时间戳: ${timestampInput ? dateTimestampSeconds : currentTimestamp}\n格式化时间: ${timestampInput ? timestampLocal : currentDatetime}\n相对时间: ${timestampInput ? timestampRelative : '当前时间'}`}
            options={{
              format: timestampFormat,
              timezone: 'local',
              includeRelativeTime: true,
            }}
            className="mx-2"
            showText={true}
          />
        </div>
      </div>

      {/* 时间戳转换区域 */}
      <div className="mb-8">
        <h2 className="text-lg font-bold mb-4 text-gray-800">{lang.timestampConversionTitle}</h2>
        <p className="text-sm text-gray-600 mb-4">{lang.timestampConversionDesc}</p>

        <input
          type="text"
          value={timestampInput}
          onChange={(e) => setTimestampInput(e.target.value)}
          placeholder={lang.timestampInputPlaceholder}
          className="w-full px-3 py-2 border border-gray-300 rounded text-sm mb-4 focus:outline-none focus:border-blue-500"
        />

        <table className="w-full border-collapse mb-4">
          <tbody>
            <tr>
              <td className="border border-gray-300 px-3 py-2 text-sm text-gray-800 w-36 bg-gray-50">
                {lang.formatLabel}
              </td>
              <td className="border border-gray-300 px-3 py-2 text-sm text-gray-800">
                {timestampFormat}
              </td>
            </tr>
            <tr>
              <td className="border border-gray-300 px-3 py-2 text-sm text-gray-800 w-36 bg-gray-50">
                {lang.gmtLabel}
              </td>
              <td className="border border-gray-300 px-3 py-2 text-sm text-gray-800">
                {timestampGmt}
              </td>
            </tr>
            <tr>
              <td className="border border-gray-300 px-3 py-2 text-sm text-gray-800 w-36 bg-gray-50">
                {lang.localTimezoneLabel}
              </td>
              <td className="border border-gray-300 px-3 py-2 text-sm text-gray-800">
                {timestampLocal}
              </td>
            </tr>
            <tr>
              <td className="border border-gray-300 px-3 py-2 text-sm text-gray-800 w-36 bg-gray-50">
                {lang.relativeTimeLabel}
              </td>
              <td className="border border-gray-300 px-3 py-2 text-sm text-gray-800">
                {timestampRelative}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* 日期转换区域 */}
      <div className="mb-8">
        <h2 className="text-lg font-bold mb-4 text-gray-800">{lang.dateConversionTitle}</h2>

        <input
          type="datetime-local"
          value={dateInput}
          onChange={(e) => setDateInput(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded text-sm mb-4 focus:outline-none focus:border-blue-500"
        />

        <table className="w-full border-collapse mb-4">
          <tbody>
            <tr>
              <td className="border border-gray-300 px-3 py-2 text-sm text-gray-800 w-36 bg-gray-50">
                {lang.secondsLabel}
              </td>
              <td className="border border-gray-300 px-3 py-2 text-sm text-gray-800">
                {dateTimestampSeconds}
              </td>
            </tr>
            <tr>
              <td className="border border-gray-300 px-3 py-2 text-sm text-gray-800 w-36 bg-gray-50">
                {lang.millisecondsLabel}
              </td>
              <td className="border border-gray-300 px-3 py-2 text-sm text-gray-800">
                {dateTimestampMilliseconds}
              </td>
            </tr>
            <tr>
              <td className="border border-gray-300 px-3 py-2 text-sm text-gray-800 w-36 bg-gray-50">
                {lang.dateGmtLabel}
              </td>
              <td className="border border-gray-300 px-3 py-2 text-sm text-gray-800">
                {dateGmt}
              </td>
            </tr>
            <tr>
              <td className="border border-gray-300 px-3 py-2 text-sm text-gray-800 w-36 bg-gray-50">
                {lang.dateLocalLabel}
              </td>
              <td className="border border-gray-300 px-3 py-2 text-sm text-gray-800">
                {dateLocal}
              </td>
            </tr>
            <tr>
              <td className="border border-gray-300 px-3 py-2 text-sm text-gray-800 w-36 bg-gray-50">
                {lang.dateRelativeLabel}
              </td>
              <td className="border border-gray-300 px-3 py-2 text-sm text-gray-800">
                {dateRelative}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* 关于 Unix 时间戳 */}
      <div className="mt-8 border-t border-gray-200 pt-5">
        <h2 className="text-lg font-bold mb-4 text-gray-800">{lang.aboutTitle}</h2>

        <div className="space-y-4 text-sm text-gray-600">
          <div>
            <h3 className="font-medium text-gray-900 mb-2">{lang.whatIsUnixTitle}</h3>
            <p>{lang.whatIsUnixDesc}</p>
          </div>

          <div>
            <h3 className="font-medium text-gray-900 mb-2">{lang.timeRangeTitle}</h3>
            <p>{lang.timeRangeStart}</p>
            <p>{lang.timeRangeEnd}</p>
            <p>{lang.timeRangeNote}</p>
          </div>

          <div>
            <h3 className="font-medium text-gray-900 mb-2">{lang.commonUnitsTitle}</h3>
            <ul className="space-y-1 pl-5">
              <li>• {lang.unitSeconds}</li>
              <li>• {lang.unitMilliseconds}</li>
              <li>• {lang.unitMicroseconds}</li>
              <li>• {lang.unitNanoseconds}</li>
            </ul>
          </div>

          <div>
            <h3 className="font-medium text-gray-900 mb-2">{lang.whyUseTitle}</h3>
            <ul className="space-y-1 pl-5">
              <li>• {lang.whyUse1}</li>
              <li>• {lang.whyUse2}</li>
              <li>• {lang.whyUse3}</li>
              <li>• {lang.whyUse4}</li>
            </ul>
          </div>

          <div>
            <h3 className="font-medium text-gray-900 mb-2">{lang.y2038Title}</h3>
            <p>{lang.y2038Desc}</p>
          </div>
        </div>
      </div>

      {/* 页脚 */}
      <div className="mt-8 pt-4 border-t border-gray-200 text-center text-xs text-gray-500">
        <span>{lang.footerText}</span>
      </div>

      {/* 右键菜单 */}
      {contextMenu.visible && (
        <div
          data-context-menu
          className={`fixed bg-white border border-gray-300 rounded-lg shadow-lg z-50 ${
            isMobile ? 'py-3' : 'py-2'
          }`}
          style={{
            left: contextMenu.x,
            top: contextMenu.y,
          }}
          onClick={(e) => e.stopPropagation()}
          onMouseDown={(e) => e.stopPropagation()}
        >
          <div
            className={`px-4 hover:bg-gray-100 cursor-pointer ${
              isMobile ? 'py-3 text-base' : 'py-2 text-sm'
            }`}
            onClick={() => {
              handleQuickCopy(contextMenu.type, 1);
              closeContextMenu();
            }}
          >
            🕐 {lang.oneMinLater}
          </div>
          <div
            className={`px-4 hover:bg-gray-100 cursor-pointer ${
              isMobile ? 'py-3 text-base' : 'py-2 text-sm'
            }`}
            onClick={() => {
              handleQuickCopy(contextMenu.type, 3);
              closeContextMenu();
            }}
          >
            🕒 {lang.threeMinLater}
          </div>
          <div
            className={`px-4 hover:bg-gray-100 cursor-pointer ${
              isMobile ? 'py-3 text-base' : 'py-2 text-sm'
            }`}
            onClick={() => {
              handleQuickCopy(contextMenu.type, 5);
              closeContextMenu();
            }}
          >
            🕔 {lang.fiveMinLater}
          </div>
          <div className="border-t border-gray-200 my-1"></div>
          <div
            className={`px-4 flex items-center gap-2 ${isMobile ? 'py-3' : 'py-2'}`}
            onClick={(e) => e.stopPropagation()}
            onMouseDown={(e) => e.stopPropagation()}
          >
            <input
              type="number"
              value={customMinutes[contextMenu.type] || ''}
              onChange={(e) => {
                e.stopPropagation();
                setCustomMinutes(prev => ({ ...prev, [contextMenu.type]: e.target.value }));
              }}
              placeholder="5"
              className={`border border-gray-300 rounded text-center focus:outline-none focus:border-blue-500 ${
                isMobile
                  ? 'w-20 px-3 py-2 text-base'
                  : 'w-16 px-2 py-1 text-xs'
              }`}
              min="1"
              max="999999"
              onClick={(e) => e.stopPropagation()}
              onMouseDown={(e) => e.stopPropagation()}
              onFocus={(e) => e.stopPropagation()}
              onKeyDown={(e) => e.stopPropagation()}
              onInput={(e) => e.stopPropagation()}
            />
            <span className={`text-gray-600 ${isMobile ? 'text-sm' : 'text-xs'}`}>
              {lang.customMinLater}
            </span>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleCustomCopy(contextMenu.type);
              }}
              onMouseDown={(e) => e.stopPropagation()}
              className={`bg-green-500 hover:bg-green-600 text-white border-none rounded cursor-pointer ${
                isMobile
                  ? 'px-3 py-2 text-sm'
                  : 'px-2 py-1 text-xs'
              }`}
            >
              {lang.customCopyBtn}
            </button>
          </div>
        </div>
      )}

      {/* 复制成功飘字提示 */}
      {copySuccess.show && (
        <div
          className="fixed top-20 left-1/2 transform -translate-x-1/2 z-50 transition-all duration-500 ease-out max-w-xs"
          style={{
            animation: 'copySuccess 3s ease-out forwards'
          }}
        >
          <div className="bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2">
            <span className="text-lg">✓</span>
            <span className="font-mono text-sm break-all">{copySuccess.message}</span>
          </div>
        </div>
      )}

      {/* 添加自定义动画样式 */}
      <style jsx>{`
        @keyframes copySuccess {
          0% {
            opacity: 0;
            transform: translateX(-50%) translateY(-20px) scale(0.8);
          }
          20% {
            opacity: 1;
            transform: translateX(-50%) translateY(0px) scale(1.1);
          }
          40% {
            transform: translateX(-50%) translateY(0px) scale(1);
          }
          80% {
            opacity: 1;
            transform: translateX(-50%) translateY(0px) scale(1);
          }
          100% {
            opacity: 0;
            transform: translateX(-50%) translateY(-10px) scale(0.9);
          }
        }
      `}</style>
    </div>
  );
};

export default UnixTimestampConverter;
