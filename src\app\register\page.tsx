'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { signIn } from 'next-auth/react';
import { <PERSON><PERSON>, Card, CardContent, CardHeader, CardTitle, Input } from '@/components/ui';
import { Eye, EyeOff, CheckCircle, XCircle, Loader2 } from 'lucide-react';
import Link from 'next/link';

interface FormErrors {
  email?: string;
  username?: string;
  password?: string;
  confirmPassword?: string;
  general?: string;
}

interface FieldValidation {
  email: boolean;
  username: boolean;
  password: boolean;
  confirmPassword: boolean;
}

export default function RegisterPage() {
  const [formData, setFormData] = useState({
    email: '',
    username: '',
    password: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [fieldValidation, setFieldValidation] = useState<FieldValidation>({
    email: false,
    username: false,
    password: false,
    confirmPassword: false,
  });
  const [checkingAvailability, setCheckingAvailability] = useState({
    email: false,
    username: false,
  });

  const router = useRouter();

  // 验证邮箱格式
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // 验证密码强度
  const validatePassword = (password: string) => {
    const hasMinLength = password.length >= 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);

    return hasMinLength && hasUpperCase && hasLowerCase && hasNumbers;
  };

  // 检查邮箱/用户名可用性
  const checkAvailability = async (field: 'email' | 'username', value: string) => {
    if (!value) return;

    setCheckingAvailability(prev => ({ ...prev, [field]: true }));

    try {
      const response = await fetch(`/api/auth/register?${field}=${encodeURIComponent(value)}`);
      const data = await response.json();

      if (data.success) {
        const check = data.checks.find((c: { field: string; available: boolean }) => c.field === field);
        if (check && !check.available) {
          setErrors(prev => ({
            ...prev,
            [field]: field === 'email' ? '该邮箱已被注册' : '该用户名已被使用',
          }));
          setFieldValidation(prev => ({ ...prev, [field]: false }));
        } else {
          setErrors(prev => ({ ...prev, [field]: undefined }));
          setFieldValidation(prev => ({ ...prev, [field]: true }));
        }
      }
    } catch (error) {
      console.error('检查可用性失败:', error);
    } finally {
      setCheckingAvailability(prev => ({ ...prev, [field]: false }));
    }
  };

  // 处理输入变化
  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setErrors(prev => ({ ...prev, [field]: undefined }));

    // 实时验证
    if (field === 'email') {
      const isValid = validateEmail(value);
      setFieldValidation(prev => ({ ...prev, email: isValid }));
      if (isValid) {
        // 延迟检查邮箱可用性
        setTimeout(() => checkAvailability('email', value), 500);
      }
    } else if (field === 'username') {
      const isValid = value.length >= 3 && /^[a-zA-Z0-9_]+$/.test(value);
      setFieldValidation(prev => ({ ...prev, username: isValid }));
      if (isValid) {
        // 延迟检查用户名可用性
        setTimeout(() => checkAvailability('username', value), 500);
      }
    } else if (field === 'password') {
      const isValid = validatePassword(value);
      setFieldValidation(prev => ({ ...prev, password: isValid }));
    } else if (field === 'confirmPassword') {
      const isValid = value === formData.password;
      setFieldValidation(prev => ({ ...prev, confirmPassword: isValid }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setErrors({});

    try {
      // 前端验证
      const newErrors: FormErrors = {};

      if (!validateEmail(formData.email)) {
        newErrors.email = '请输入有效的邮箱地址';
      }

      if (formData.username.length < 3) {
        newErrors.username = '用户名至少需要3个字符';
      }

      if (!validatePassword(formData.password)) {
        newErrors.password = '密码必须包含大小写字母和数字，至少8个字符';
      }

      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = '两次输入的密码不一致';
      }

      if (Object.keys(newErrors).length > 0) {
        setErrors(newErrors);
        setLoading(false);
        return;
      }

      // 发送注册请求
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          username: formData.username,
          password: formData.password,
          confirmPassword: formData.confirmPassword,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess(true);

        // 注册成功，自动登录
        const signInResult = await signIn('credentials', {
          email: formData.email,
          password: formData.password,
          redirect: false,
        });

        if (signInResult?.error) {
          // 注册成功但登录失败，跳转到登录页
          setTimeout(() => {
            router.push('/login?message=注册成功，请登录');
          }, 2000);
        } else {
          // 注册并登录成功，跳转到首页
          setTimeout(() => {
            router.push('/?welcome=true');
          }, 2000);
        }
      } else {
        // 注册失败
        if (data.field) {
          setErrors({ [data.field]: data.message });
        } else {
          setErrors({ general: data.message });
        }
      }
    } catch (error) {
      console.error('注册失败:', error);
      setErrors({ general: '注册失败，请稍后重试' });
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full">
          <Card>
            <CardContent className="text-center py-8">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">注册成功！</h3>
              <p className="text-gray-600 mb-4">您的账户已创建成功，正在跳转到登录页面...</p>
              <Button onClick={() => router.push('/login')}>
                立即登录
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            创建新账户
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            或者{' '}
            <Link href="/login" className="font-medium text-primary-600 hover:text-primary-500">
              登录现有账户
            </Link>
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>账户注册</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              {errors.general && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                  {errors.general}
                </div>
              )}

              {/* 邮箱 */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                  邮箱地址 *
                </label>
                <div className="mt-1 relative">
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    required
                    placeholder="请输入邮箱地址"
                    className={`pr-10 ${errors.email ? 'border-red-300' : fieldValidation.email ? 'border-green-300' : ''}`}
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                    {checkingAvailability.email ? (
                      <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                    ) : fieldValidation.email ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : formData.email && !fieldValidation.email ? (
                      <XCircle className="h-4 w-4 text-red-500" />
                    ) : null}
                  </div>
                </div>
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600">{errors.email}</p>
                )}
              </div>

              {/* 用户名 */}
              <div>
                <label htmlFor="username" className="block text-sm font-medium text-gray-700">
                  用户名 *
                </label>
                <div className="mt-1 relative">
                  <Input
                    id="username"
                    type="text"
                    value={formData.username}
                    onChange={(e) => handleInputChange('username', e.target.value)}
                    required
                    placeholder="3-20个字符，只能包含字母、数字和下划线"
                    className={`pr-10 ${errors.username ? 'border-red-300' : fieldValidation.username ? 'border-green-300' : ''}`}
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                    {checkingAvailability.username ? (
                      <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                    ) : fieldValidation.username ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : formData.username && !fieldValidation.username ? (
                      <XCircle className="h-4 w-4 text-red-500" />
                    ) : null}
                  </div>
                </div>
                {errors.username && (
                  <p className="mt-1 text-sm text-red-600">{errors.username}</p>
                )}
              </div>

              {/* 密码 */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  密码 *
                </label>
                <div className="mt-1 relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    required
                    placeholder="至少8个字符，包含大小写字母和数字"
                    className={`pr-10 ${errors.password ? 'border-red-300' : fieldValidation.password ? 'border-green-300' : ''}`}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400" />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="mt-1 text-sm text-red-600">{errors.password}</p>
                )}
              </div>

              {/* 确认密码 */}
              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                  确认密码 *
                </label>
                <div className="mt-1 relative">
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={formData.confirmPassword}
                    onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                    required
                    placeholder="请再次输入密码"
                    className={`pr-10 ${errors.confirmPassword ? 'border-red-300' : fieldValidation.confirmPassword ? 'border-green-300' : ''}`}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400" />
                    )}
                  </button>
                </div>
                {errors.confirmPassword && (
                  <p className="mt-1 text-sm text-red-600">{errors.confirmPassword}</p>
                )}
              </div>

              <div className="text-xs text-gray-500">
                注册即表示您同意我们的{' '}
                <Link href="/terms" className="text-primary-600 hover:text-primary-500">
                  服务条款
                </Link>{' '}
                和{' '}
                <Link href="/privacy" className="text-primary-600 hover:text-primary-500">
                  隐私政策
                </Link>
              </div>

              <Button
                type="submit"
                disabled={loading || !Object.values(fieldValidation).every(Boolean)}
                className="w-full"
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    注册中...
                  </>
                ) : (
                  '注册账户'
                )}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* 演示提示 */}
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-3">
              <div className="text-sm text-gray-600">
                <strong>演示提示：</strong> 当前为演示版本，注册功能已完全实现。
              </div>
              <div className="text-sm text-gray-500">
                您也可以使用演示账户直接登录体验功能。
              </div>
              <div className="text-xs text-gray-400">
                如需演示账户信息，请联系开发者。
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 返回首页 */}
        <div className="text-center">
          <Button
            variant="ghost"
            onClick={() => router.push('/')}
            className="text-sm"
          >
            ← 返回首页
          </Button>
        </div>
      </div>
    </div>
  );
}
