import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db/mongodb';
import User from '@/lib/db/models/User';

export async function POST(_request: NextRequest) {
  try {
    // 连接数据库
    await connectDB();

    // 检查是否已经存在演示用户
    const existingAdmin = await User.findOne({ email: '<EMAIL>' });
    const existingUser = await User.findOne({ email: '<EMAIL>' });

    const results = [];

    // 创建演示管理员账户
    if (!existingAdmin) {
      const adminUser = new User({
        email: '<EMAIL>',
        username: 'admin',
        password: 'admin123',
        role: 'admin',
        isEmailVerified: true,
        providers: [{
          provider: 'credentials',
          providerId: '<EMAIL>',
        }],
        preferences: {
          theme: 'auto',
          language: 'zh-CN',
          favoriteTools: ['timestamp-converter', 'json-formatter'],
          recentTools: [
            {
              toolId: 'timestamp-converter',
              lastUsed: new Date(),
            },
            {
              toolId: 'json-formatter',
              lastUsed: new Date(Date.now() - 1000 * 60 * 30), // 30分钟前
            },
          ],
        },
      });

      await adminUser.save();
      results.push({
        type: 'admin',
        email: '<EMAIL>',
        username: 'admin',
        status: 'created',
      });
    } else {
      results.push({
        type: 'admin',
        email: '<EMAIL>',
        username: 'admin',
        status: 'already_exists',
      });
    }

    // 创建演示普通用户账户
    if (!existingUser) {
      const normalUser = new User({
        email: '<EMAIL>',
        username: 'user',
        password: 'user123',
        role: 'user',
        isEmailVerified: true,
        providers: [{
          provider: 'credentials',
          providerId: '<EMAIL>',
        }],
        preferences: {
          theme: 'light',
          language: 'zh-CN',
          favoriteTools: ['url-encoder', 'case-converter'],
          recentTools: [
            {
              toolId: 'url-encoder',
              lastUsed: new Date(),
            },
            {
              toolId: 'case-converter',
              lastUsed: new Date(Date.now() - 1000 * 60 * 15), // 15分钟前
            },
          ],
        },
      });

      await normalUser.save();
      results.push({
        type: 'user',
        email: '<EMAIL>',
        username: 'user',
        status: 'created',
      });
    } else {
      results.push({
        type: 'user',
        email: '<EMAIL>',
        username: 'user',
        status: 'already_exists',
      });
    }

    return NextResponse.json({
      success: true,
      message: '演示用户初始化完成',
      results,
    });

  } catch (error) {
    console.error('初始化演示用户失败:', error);
    
    return NextResponse.json({
      success: false,
      message: '初始化演示用户失败',
      error: process.env.NODE_ENV === 'development' ? error instanceof Error ? error.message : '未知错误' : undefined,
    }, { status: 500 });
  }
}

// 获取演示用户状态
export async function GET() {
  try {
    await connectDB();

    const adminUser = await User.findOne({ email: '<EMAIL>' }).select('-password');
    const normalUser = await User.findOne({ email: '<EMAIL>' }).select('-password');

    return NextResponse.json({
      success: true,
      users: {
        admin: adminUser ? {
          id: adminUser._id,
          email: adminUser.email,
          username: adminUser.username,
          role: adminUser.role,
          createdAt: adminUser.createdAt,
          lastLoginAt: adminUser.lastLoginAt,
          isEmailVerified: adminUser.isEmailVerified,
        } : null,
        user: normalUser ? {
          id: normalUser._id,
          email: normalUser.email,
          username: normalUser.username,
          role: normalUser.role,
          createdAt: normalUser.createdAt,
          lastLoginAt: normalUser.lastLoginAt,
          isEmailVerified: normalUser.isEmailVerified,
        } : null,
      },
    });

  } catch (error) {
    console.error('获取演示用户状态失败:', error);
    
    return NextResponse.json({
      success: false,
      message: '获取演示用户状态失败',
    }, { status: 500 });
  }
}
