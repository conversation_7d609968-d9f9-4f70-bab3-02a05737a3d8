import { MetadataRoute } from 'next';

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://cypress.fun';
  
  // 静态页面
  const staticPages = [
    '',
    '/tools',
    '/navigation',
    '/search',
    '/privacy',
    '/terms',
    '/about',
    '/contact',
    '/help',
    '/feedback',
    '/login',
    '/register',
    '/profile',
    '/favorites',
    '/history',
  ];

  // 工具页面
  const toolPages = [
    '/tools/timestamp',
    '/tools/json-formatter',
    '/tools/base64-converter',
    '/tools/color-converter',
    '/tools/case-converter',
    '/tools/url-encoder',
    '/tools/ip-converter',
    '/tools/qr-generator',
    '/tools/sha-hash',
    '/tools/image-compressor',
    '/tools/text-converter',
  ];

  // 工具特性页面
  const toolFeaturePages = [
    '/tools/timestamp/features',
  ];

  const currentDate = new Date();

  return [
    // 首页 - 最高优先级
    {
      url: baseUrl,
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 1,
    },
    // 工具页面 - 高优先级
    ...toolPages.map((path) => ({
      url: `${baseUrl}${path}`,
      lastModified: currentDate,
      changeFrequency: 'weekly' as const,
      priority: 0.9,
    })),
    // 工具特性页面 - 中高优先级
    ...toolFeaturePages.map((path) => ({
      url: `${baseUrl}${path}`,
      lastModified: currentDate,
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    })),
    // 其他静态页面 - 中等优先级
    ...staticPages.slice(1).map((path) => ({
      url: `${baseUrl}${path}`,
      lastModified: currentDate,
      changeFrequency: 'monthly' as const,
      priority: 0.7,
    })),
  ];
}
