'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { <PERSON><PERSON>, Card, CardContent, CardHeader, CardTitle } from '@/components/ui';
import ShareButton from '@/components/share/ShareButton';

type QRType = 'text' | 'url' | 'email' | 'phone' | 'sms' | 'wifi';

interface QRConfig {
  size: number;
  margin: number;
  colorDark: string;
  colorLight: string;
  errorCorrectionLevel: 'L' | 'M' | 'Q' | 'H';
}

const QRGeneratorPage: React.FC = () => {
  const [qrType, setQRType] = useState<QRType>('text');
  const [content, setContent] = useState<string>('');
  const [qrDataURL, setQrDataURL] = useState<string>('');
  const [config, setConfig] = useState<QRConfig>({
    size: 256,
    margin: 4,
    colorDark: '#000000',
    colorLight: '#FFFFFF',
    errorCorrectionLevel: 'M',
  });


  // 简单的二维码生成函数（实际项目中应使用专业库如qrcode.js）
  const generateQRCode = async (text: string, config: QRConfig) => {
    if (!text.trim()) {
      setQrDataURL('');
      return;
    }

    try {
      // 这里使用一个简单的二维码API服务作为示例
      // 实际项目中建议使用客户端库如qrcode.js
      const qrApiUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${config.size}x${config.size}&data=${encodeURIComponent(text)}&color=${config.colorDark.replace('#', '')}&bgcolor=${config.colorLight.replace('#', '')}&margin=${config.margin}&ecc=${config.errorCorrectionLevel}`;

      setQrDataURL(qrApiUrl);
    } catch (error) {
      console.error('生成二维码失败:', error);
    }
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      generateQRCode(content, config);
    }, 500);

    return () => clearTimeout(timer);
  }, [content, config]);

  const qrTypes = [
    { value: 'text' as QRType, label: '📝 纯文本', placeholder: '输入要编码的文本...' },
    { value: 'url' as QRType, label: '🔗 网址链接', placeholder: 'https://example.com' },
    { value: 'email' as QRType, label: '📧 邮箱地址', placeholder: '<EMAIL>' },
    { value: 'phone' as QRType, label: '📞 电话号码', placeholder: '+86 138 0013 8000' },
    { value: 'sms' as QRType, label: '💬 短信', placeholder: '手机号码:短信内容' },
    { value: 'wifi' as QRType, label: '📶 WiFi信息', placeholder: 'SSID:密码:加密类型' },
  ];

  const errorLevels = [
    { value: 'L' as const, label: 'L (低)', description: '约7%错误恢复' },
    { value: 'M' as const, label: 'M (中)', description: '约15%错误恢复' },
    { value: 'Q' as const, label: 'Q (高)', description: '约25%错误恢复' },
    { value: 'H' as const, label: 'H (最高)', description: '约30%错误恢复' },
  ];

  const formatContent = (type: QRType, input: string): string => {
    switch (type) {
      case 'email':
        return input.includes('@') ? `mailto:${input}` : input;
      case 'phone':
        return `tel:${input}`;
      case 'sms':
        const [phone, message] = input.split(':');
        return `sms:${phone}${message ? `?body=${encodeURIComponent(message)}` : ''}`;
      case 'wifi':
        const [ssid, password, security = 'WPA'] = input.split(':');
        return `WIFI:T:${security};S:${ssid};P:${password};;`;
      default:
        return input;
    }
  };

  const handleContentChange = (value: string) => {
    setContent(formatContent(qrType, value));
  };

  const downloadQR = () => {
    if (!qrDataURL) return;

    const link = document.createElement('a');
    link.download = 'qrcode.png';
    link.href = qrDataURL;
    link.click();
  };

  const copyQRImage = async () => {
    if (!qrDataURL) return;

    try {
      const response = await fetch(qrDataURL);
      const blob = await response.blob();
      await navigator.clipboard.write([
        new ClipboardItem({ 'image/png': blob })
      ]);
      alert('二维码已复制到剪贴板');
    } catch (error) {
      console.error('复制失败:', error);
      alert('复制失败，请使用下载功能');
    }
  };

  const presetExamples = {
    text: '这是一个示例文本',
    url: 'https://github.com',
    email: '<EMAIL>',
    phone: '+86 138 0013 8000',
    sms: '138001380000:你好，这是一条测试短信',
    wifi: 'MyWiFi:password123:WPA',
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">二维码生成工具</h1>
          <p className="text-gray-600">
            生成各种类型的二维码，支持文本、链接、邮箱、电话等多种格式
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 左侧：配置面板 */}
          <div className="space-y-6">
            {/* 二维码类型 */}
            <Card>
              <CardHeader>
                <CardTitle>二维码类型</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-3">
                  {qrTypes.map((type) => (
                    <button
                      key={type.value}
                      onClick={() => {
                        setQRType(type.value);
                        setContent('');
                      }}
                      className={`p-3 text-left border rounded-lg transition-all ${
                        qrType === type.value
                          ? 'border-primary-500 bg-primary-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="font-medium text-sm">{type.label}</div>
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 内容输入 */}
            <Card>
              <CardHeader>
                <CardTitle>内容输入</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <textarea
                    rows={4}
                    value={content}
                    onChange={(e) => handleContentChange(e.target.value)}
                    placeholder={qrTypes.find(t => t.value === qrType)?.placeholder}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setContent(presetExamples[qrType])}
                  >
                    💡 使用示例
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* 样式配置 */}
            <Card>
              <CardHeader>
                <CardTitle>样式配置</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* 尺寸 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    尺寸: {config.size}px
                  </label>
                  <input
                    type="range"
                    min="128"
                    max="512"
                    step="32"
                    value={config.size}
                    onChange={(e) => setConfig(prev => ({ ...prev, size: parseInt(e.target.value) }))}
                    className="w-full"
                  />
                </div>

                {/* 边距 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    边距: {config.margin}
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="10"
                    value={config.margin}
                    onChange={(e) => setConfig(prev => ({ ...prev, margin: parseInt(e.target.value) }))}
                    className="w-full"
                  />
                </div>

                {/* 颜色 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      前景色
                    </label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="color"
                        value={config.colorDark}
                        onChange={(e) => setConfig(prev => ({ ...prev, colorDark: e.target.value }))}
                        className="w-10 h-8 rounded border"
                      />
                      <span className="text-sm font-mono">{config.colorDark}</span>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      背景色
                    </label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="color"
                        value={config.colorLight}
                        onChange={(e) => setConfig(prev => ({ ...prev, colorLight: e.target.value }))}
                        className="w-10 h-8 rounded border"
                      />
                      <span className="text-sm font-mono">{config.colorLight}</span>
                    </div>
                  </div>
                </div>

                {/* 错误纠正级别 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    错误纠正级别
                  </label>
                  <select
                    value={config.errorCorrectionLevel}
                    onChange={(e) => setConfig(prev => ({ ...prev, errorCorrectionLevel: e.target.value as 'L' | 'M' | 'Q' | 'H' }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    {errorLevels.map((level) => (
                      <option key={level.value} value={level.value}>
                        {level.label} - {level.description}
                      </option>
                    ))}
                  </select>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 右侧：二维码预览 */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>二维码预览</CardTitle>
                  {qrDataURL && content && (
                    <ShareButton
                      toolId="qr-generator"
                      toolName="二维码生成器"
                      input={content}
                      output="二维码图片已生成"
                      options={{
                        qrType,
                        size: config.size,
                        margin: config.margin,
                        colorDark: config.colorDark,
                        colorLight: config.colorLight,
                        errorCorrectionLevel: config.errorCorrectionLevel,
                        qrDataURL,
                      }}
                      size="sm"
                      showText={false}
                    />
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-center space-y-4">
                  {qrDataURL ? (
                    <>
                      <div className="inline-block p-4 bg-white border-2 border-gray-200 rounded-lg">
                        <Image
                          src={qrDataURL}
                          alt="Generated QR Code"
                          width={config.size}
                          height={config.size}
                          className="max-w-full h-auto"
                        />
                      </div>

                      <div className="flex justify-center space-x-3">
                        <Button onClick={downloadQR}>
                          📥 下载
                        </Button>
                        <Button variant="outline" onClick={copyQRImage}>
                          📋 复制
                        </Button>
                      </div>
                    </>
                  ) : (
                    <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
                      <div className="text-center text-gray-500">
                        <div className="text-4xl mb-2">📱</div>
                        <div>请输入内容生成二维码</div>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* 使用说明 */}
            <Card>
              <CardHeader>
                <CardTitle>使用说明</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-gray-600 space-y-2">
                  <p><strong>支持的格式:</strong></p>
                  <ul className="list-disc list-inside ml-4 space-y-1">
                    <li><strong>纯文本:</strong> 任意文本内容</li>
                    <li><strong>网址:</strong> 自动添加协议前缀</li>
                    <li><strong>邮箱:</strong> 生成邮件链接</li>
                    <li><strong>电话:</strong> 生成拨号链接</li>
                    <li><strong>短信:</strong> 格式：手机号:短信内容</li>
                    <li><strong>WiFi:</strong> 格式：SSID:密码:加密类型</li>
                  </ul>
                  <p><strong>错误纠正:</strong> 级别越高，二维码越复杂但容错性越强</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QRGeneratorPage;
