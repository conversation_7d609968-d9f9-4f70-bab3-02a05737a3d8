import { Metadata } from 'next';

export const metadata: Metadata = {
  title: '服务条款 - Tool List',
  description: 'Tool List 服务条款，了解使用我们服务的条件和规则。',
};

export default function TermsPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">服务条款</h1>
        
        <div className="prose prose-lg max-w-none">
          <p className="text-gray-600 mb-6">
            最后更新时间：2024年12月20日
          </p>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">1. 服务说明</h2>
            <p className="text-gray-700 mb-4">
              Tool List 是一个免费的在线开发工具集合平台，提供多种实用的开发工具。
            </p>
            <ul className="list-disc list-inside text-gray-700 space-y-2">
              <li>所有工具均可免费使用</li>
              <li>无需注册即可使用基本功能</li>
              <li>我们保留随时修改或终止服务的权利</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">2. 使用规则</h2>
            <p className="text-gray-700 mb-4">
              使用我们的服务时，您同意：
            </p>
            <ul className="list-disc list-inside text-gray-700 space-y-2">
              <li>不进行任何非法或有害的活动</li>
              <li>不尝试破坏或干扰我们的服务</li>
              <li>不滥用我们的服务器资源</li>
              <li>遵守所有适用的法律法规</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">3. 知识产权</h2>
            <p className="text-gray-700 mb-4">
              Tool List 的所有内容和功能均受知识产权法保护：
            </p>
            <ul className="list-disc list-inside text-gray-700 space-y-2">
              <li>网站设计和代码归Tool List所有</li>
              <li>用户生成的内容归用户所有</li>
              <li>未经许可不得复制或分发我们的内容</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">4. 免责声明</h2>
            <p className="text-gray-700 mb-4">
              我们的服务按"现状"提供，不提供任何明示或暗示的保证：
            </p>
            <ul className="list-disc list-inside text-gray-700 space-y-2">
              <li>不保证服务的连续性或无错误</li>
              <li>不对使用我们服务造成的任何损失负责</li>
              <li>用户应自行承担使用风险</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">5. 服务变更</h2>
            <p className="text-gray-700 mb-4">
              我们保留随时修改这些条款的权利。重大变更将通过网站通知用户。
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">6. 联系我们</h2>
            <p className="text-gray-700">
              如果您对本服务条款有任何疑问，请联系我们：
            </p>
            <p className="text-gray-700 mt-2">
              邮箱：<EMAIL>
            </p>
          </section>
        </div>
      </div>
    </div>
  );
}
