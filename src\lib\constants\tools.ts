import { Tool, ToolCategory } from '@/types/tools';

// 工具分类
export const TOOL_CATEGORIES: ToolCategory[] = [
  {
    id: 'time',
    name: '时间工具',
    icon: '🕒',
    color: '#3b82f6',
    description: '时间戳转换、时区转换等时间相关工具',
    order: 1,
    isPublic: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'text',
    name: '文本工具',
    icon: '📝',
    color: '#10b981',
    description: '文本格式化、编码转换、大小写转换等',
    order: 2,
    isPublic: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'format',
    name: '格式化工具',
    icon: '🔧',
    color: '#f59e0b',
    description: 'JSON、XML、CSS等格式化和美化工具',
    order: 3,
    isPublic: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'network',
    name: '网络工具',
    icon: '🌐',
    color: '#ef4444',
    description: 'IP地址转换、URL编码、网络诊断等',
    order: 4,
    isPublic: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'crypto',
    name: '加密工具',
    icon: '🔐',
    color: '#8b5cf6',
    description: 'MD5、SHA、Base64等加密解密工具',
    order: 5,
    isPublic: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'image',
    name: '图片工具',
    icon: '🖼️',
    color: '#ec4899',
    description: '图片压缩、格式转换、尺寸调整等',
    order: 6,
    isPublic: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'design',
    name: '设计工具',
    icon: '🎨',
    color: '#f97316',
    description: '颜色转换、设计辅助、UI工具等',
    order: 7,
    isPublic: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'utility',
    name: '实用工具',
    icon: '🛠️',
    color: '#06b6d4',
    description: '二维码生成、实用小工具等',
    order: 8,
    isPublic: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

// 工具列表
export const TOOLS: Tool[] = [
  {
    id: 'timestamp-converter',
    name: 'Unix时间戳转换',
    description: '在Unix时间戳和标准时间格式之间进行转换，支持多种时区和格式',
    icon: '🕒',
    category: 'time',
    tags: ['时间戳', 'Unix', '时间转换', '时区'],
    path: '/tools/timestamp',
    isPublic: true,
    requiredAuth: false,
    visitCount: 0,
    addedBy: 'system',
    config: {
      maxInputSize: 1024,
      rateLimit: {
        requests: 100,
        window: 60000,
      },
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'json-formatter',
    name: 'JSON格式化',
    description: 'JSON数据格式化、压缩、验证和美化工具',
    icon: '📋',
    category: 'format',
    tags: ['JSON', '格式化', '美化', '验证'],
    path: '/tools/json-formatter',
    isPublic: true,
    requiredAuth: false,
    visitCount: 0,
    addedBy: 'system',
    config: {
      maxInputSize: 1048576, // 1MB
      rateLimit: {
        requests: 50,
        window: 60000,
      },
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'text-converter',
    name: '文本转换',
    description: '文本大小写转换、编码解码、格式转换等',
    icon: '📝',
    category: 'text',
    tags: ['文本', '大小写', '编码', 'Base64', 'URL编码'],
    path: '/tools/text-converter',
    isPublic: true,
    requiredAuth: false,
    visitCount: 0,
    addedBy: 'system',
    config: {
      maxInputSize: 524288, // 512KB
      rateLimit: {
        requests: 100,
        window: 60000,
      },
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'ip-converter',
    name: 'IP地址转换',
    description: 'IP地址格式转换，支持IPv4、IPv6、十进制等格式',
    icon: '🌐',
    category: 'network',
    tags: ['IP地址', 'IPv4', 'IPv6', '网络', '十进制'],
    path: '/tools/ip-converter',
    isPublic: true,
    requiredAuth: false,
    visitCount: 0,
    addedBy: 'system',
    config: {
      maxInputSize: 256,
      rateLimit: {
        requests: 200,
        window: 60000,
      },
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'base64-converter',
    name: 'Base64编码',
    description: 'Base64编码和解码工具',
    icon: '🔤',
    category: 'crypto',
    tags: ['Base64', '编码', '解码'],
    path: '/tools/base64-converter',
    isPublic: true,
    requiredAuth: false,
    visitCount: 0,
    addedBy: 'system',
    config: {
      maxInputSize: 1048576, // 1MB
      rateLimit: {
        requests: 100,
        window: 60000,
      },
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'case-converter',
    name: '大小写转换',
    description: '文本大小写转换工具，支持多种命名格式转换',
    icon: '🔤',
    category: 'text',
    tags: ['大小写', '驼峰', '下划线', '短横线', '文本转换'],
    path: '/tools/case-converter',
    isPublic: true,
    requiredAuth: false,
    visitCount: 0,
    addedBy: 'system',
    config: {
      maxInputSize: 524288, // 512KB
      rateLimit: {
        requests: 200,
        window: 60000,
      },
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'image-compressor',
    name: '图片压缩',
    description: '在线压缩图片，减小文件大小，支持多种格式',
    icon: '🖼️',
    category: 'image',
    tags: ['图片压缩', '图片优化', '文件大小', '图片处理'],
    path: '/tools/image-compressor',
    isPublic: true,
    requiredAuth: false,
    visitCount: 0,
    addedBy: 'system',
    config: {
      maxInputSize: 10485760, // 10MB
      rateLimit: {
        requests: 50,
        window: 60000,
      },
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'sha-hash',
    name: 'SHA哈希计算',
    description: '计算SHA-1、SHA-256、SHA-384、SHA-512哈希值',
    icon: '🔐',
    category: 'crypto',
    tags: ['SHA', '哈希', '加密', '校验', '安全'],
    path: '/tools/sha-hash',
    isPublic: true,
    requiredAuth: false,
    visitCount: 0,
    addedBy: 'system',
    config: {
      maxInputSize: 1048576, // 1MB
      rateLimit: {
        requests: 100,
        window: 60000,
      },
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'url-encoder',
    name: 'URL编码解码',
    description: 'URL编码和解码工具，处理特殊字符',
    icon: '🔗',
    category: 'network',
    tags: ['URL编码', '编码解码', 'URI', '特殊字符'],
    path: '/tools/url-encoder',
    isPublic: true,
    requiredAuth: false,
    visitCount: 0,
    addedBy: 'system',
    config: {
      maxInputSize: 1048576, // 1MB
      rateLimit: {
        requests: 100,
        window: 60000,
      },
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'color-converter',
    name: '颜色转换',
    description: '在HEX、RGB、HSL、HSV、CMYK等颜色格式间转换',
    icon: '🎨',
    category: 'design',
    tags: ['颜色', '转换', 'HEX', 'RGB', 'HSL', '设计'],
    path: '/tools/color-converter',
    isPublic: true,
    requiredAuth: false,
    visitCount: 0,
    addedBy: 'system',
    config: {
      maxInputSize: 1024,
      rateLimit: {
        requests: 200,
        window: 60000,
      },
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'qr-generator',
    name: '二维码生成',
    description: '生成各种类型的二维码，支持文本、链接、WiFi等',
    icon: '📱',
    category: 'utility',
    tags: ['二维码', 'QR码', '生成器', '链接', 'WiFi'],
    path: '/tools/qr-generator',
    isPublic: true,
    requiredAuth: false,
    visitCount: 0,
    addedBy: 'system',
    config: {
      maxInputSize: 2048,
      rateLimit: {
        requests: 100,
        window: 60000,
      },
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

// 工具操作类型
export const TOOL_ACTIONS = {
  VIEW: 'view',
  USE: 'use',
  DOWNLOAD: 'download',
  SHARE: 'share',
  FAVORITE: 'favorite',
} as const;

// 文本转换操作
export const TEXT_OPERATIONS = {
  UPPERCASE: 'uppercase',
  LOWERCASE: 'lowercase',
  CAPITALIZE: 'capitalize',
  CAMEL_CASE: 'camelCase',
  SNAKE_CASE: 'snakeCase',
  KEBAB_CASE: 'kebabCase',
  BASE64_ENCODE: 'base64Encode',
  BASE64_DECODE: 'base64Decode',
  URL_ENCODE: 'urlEncode',
  URL_DECODE: 'urlDecode',
  HTML_ENCODE: 'htmlEncode',
  HTML_DECODE: 'htmlDecode',
} as const;

// 时间格式
export const TIME_FORMATS = {
  ISO: 'iso',
  LOCAL: 'local',
  UTC: 'utc',
  CUSTOM: 'custom',
} as const;

// IP地址类型
export const IP_TYPES = {
  IPV4: 'ipv4',
  IPV6: 'ipv6',
  DECIMAL: 'decimal',
  HEX: 'hex',
  BINARY: 'binary',
} as const;

// 工具默认配置
export const DEFAULT_TOOL_CONFIG = {
  maxInputSize: 1048576, // 1MB
  rateLimit: {
    requests: 100,
    window: 60000, // 1分钟
  },
};

// 工具分类颜色映射
export const CATEGORY_COLORS: Record<string, string> = {
  time: '#3b82f6',
  text: '#10b981',
  format: '#f59e0b',
  network: '#ef4444',
  crypto: '#8b5cf6',
  image: '#ec4899',
  design: '#f97316',
  utility: '#06b6d4',
};

// 工具图标映射
export const TOOL_ICONS: Record<string, string> = {
  'timestamp-converter': '🕒',
  'json-formatter': '📋',
  'text-converter': '📝',
  'ip-converter': '🌐',
  'base64-converter': '🔤',
  'case-converter': '🔤',
  'image-compressor': '🖼️',
  'sha-hash': '🔐',
  'url-encoder': '🔗',
  'color-converter': '🎨',
  'qr-generator': '📱',
};
